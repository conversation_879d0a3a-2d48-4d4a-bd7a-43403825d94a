// Code generated by mockery v2.52.3. DO NOT EDIT.

package ccipocr3

import (
	context "context"

	ccipocr3 "github.com/smartcontractkit/chainlink-common/pkg/types/ccipocr3"

	mock "github.com/stretchr/testify/mock"

	primitives "github.com/smartcontractkit/chainlink-common/pkg/types/query/primitives"

	time "time"
)

// MockChainAccessor is an autogenerated mock type for the ChainAccessor type
type MockChainAccessor struct {
	mock.Mock
}

type MockChainAccessor_Expecter struct {
	mock *mock.Mock
}

func (_m *MockChainAccessor) EXPECT() *MockChainAccessor_Expecter {
	return &MockChainAccessor_Expecter{mock: &_m.Mock}
}

// CommitReportsGTETimestamp provides a mock function with given fields: ctx, ts, confidence, limit
func (_m *MockChainAccessor) CommitReportsGTETimestamp(ctx context.Context, ts time.Time, confidence primitives.ConfidenceLevel, limit int) ([]ccipocr3.CommitPluginReportWithMeta, error) {
	ret := _m.Called(ctx, ts, confidence, limit)

	if len(ret) == 0 {
		panic("no return value specified for CommitReportsGTETimestamp")
	}

	var r0 []ccipocr3.CommitPluginReportWithMeta
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, time.Time, primitives.ConfidenceLevel, int) ([]ccipocr3.CommitPluginReportWithMeta, error)); ok {
		return rf(ctx, ts, confidence, limit)
	}
	if rf, ok := ret.Get(0).(func(context.Context, time.Time, primitives.ConfidenceLevel, int) []ccipocr3.CommitPluginReportWithMeta); ok {
		r0 = rf(ctx, ts, confidence, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]ccipocr3.CommitPluginReportWithMeta)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, time.Time, primitives.ConfidenceLevel, int) error); ok {
		r1 = rf(ctx, ts, confidence, limit)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockChainAccessor_CommitReportsGTETimestamp_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CommitReportsGTETimestamp'
type MockChainAccessor_CommitReportsGTETimestamp_Call struct {
	*mock.Call
}

// CommitReportsGTETimestamp is a helper method to define mock.On call
//   - ctx context.Context
//   - ts time.Time
//   - confidence primitives.ConfidenceLevel
//   - limit int
func (_e *MockChainAccessor_Expecter) CommitReportsGTETimestamp(ctx interface{}, ts interface{}, confidence interface{}, limit interface{}) *MockChainAccessor_CommitReportsGTETimestamp_Call {
	return &MockChainAccessor_CommitReportsGTETimestamp_Call{Call: _e.mock.On("CommitReportsGTETimestamp", ctx, ts, confidence, limit)}
}

func (_c *MockChainAccessor_CommitReportsGTETimestamp_Call) Run(run func(ctx context.Context, ts time.Time, confidence primitives.ConfidenceLevel, limit int)) *MockChainAccessor_CommitReportsGTETimestamp_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(time.Time), args[2].(primitives.ConfidenceLevel), args[3].(int))
	})
	return _c
}

func (_c *MockChainAccessor_CommitReportsGTETimestamp_Call) Return(_a0 []ccipocr3.CommitPluginReportWithMeta, _a1 error) *MockChainAccessor_CommitReportsGTETimestamp_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockChainAccessor_CommitReportsGTETimestamp_Call) RunAndReturn(run func(context.Context, time.Time, primitives.ConfidenceLevel, int) ([]ccipocr3.CommitPluginReportWithMeta, error)) *MockChainAccessor_CommitReportsGTETimestamp_Call {
	_c.Call.Return(run)
	return _c
}

// ExecutedMessages provides a mock function with given fields: ctx, ranges, confidence
func (_m *MockChainAccessor) ExecutedMessages(ctx context.Context, ranges map[ccipocr3.ChainSelector][]ccipocr3.SeqNumRange, confidence primitives.ConfidenceLevel) (map[ccipocr3.ChainSelector][]ccipocr3.SeqNum, error) {
	ret := _m.Called(ctx, ranges, confidence)

	if len(ret) == 0 {
		panic("no return value specified for ExecutedMessages")
	}

	var r0 map[ccipocr3.ChainSelector][]ccipocr3.SeqNum
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, map[ccipocr3.ChainSelector][]ccipocr3.SeqNumRange, primitives.ConfidenceLevel) (map[ccipocr3.ChainSelector][]ccipocr3.SeqNum, error)); ok {
		return rf(ctx, ranges, confidence)
	}
	if rf, ok := ret.Get(0).(func(context.Context, map[ccipocr3.ChainSelector][]ccipocr3.SeqNumRange, primitives.ConfidenceLevel) map[ccipocr3.ChainSelector][]ccipocr3.SeqNum); ok {
		r0 = rf(ctx, ranges, confidence)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[ccipocr3.ChainSelector][]ccipocr3.SeqNum)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, map[ccipocr3.ChainSelector][]ccipocr3.SeqNumRange, primitives.ConfidenceLevel) error); ok {
		r1 = rf(ctx, ranges, confidence)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockChainAccessor_ExecutedMessages_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ExecutedMessages'
type MockChainAccessor_ExecutedMessages_Call struct {
	*mock.Call
}

// ExecutedMessages is a helper method to define mock.On call
//   - ctx context.Context
//   - ranges map[ccipocr3.ChainSelector][]ccipocr3.SeqNumRange
//   - confidence primitives.ConfidenceLevel
func (_e *MockChainAccessor_Expecter) ExecutedMessages(ctx interface{}, ranges interface{}, confidence interface{}) *MockChainAccessor_ExecutedMessages_Call {
	return &MockChainAccessor_ExecutedMessages_Call{Call: _e.mock.On("ExecutedMessages", ctx, ranges, confidence)}
}

func (_c *MockChainAccessor_ExecutedMessages_Call) Run(run func(ctx context.Context, ranges map[ccipocr3.ChainSelector][]ccipocr3.SeqNumRange, confidence primitives.ConfidenceLevel)) *MockChainAccessor_ExecutedMessages_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(map[ccipocr3.ChainSelector][]ccipocr3.SeqNumRange), args[2].(primitives.ConfidenceLevel))
	})
	return _c
}

func (_c *MockChainAccessor_ExecutedMessages_Call) Return(_a0 map[ccipocr3.ChainSelector][]ccipocr3.SeqNum, _a1 error) *MockChainAccessor_ExecutedMessages_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockChainAccessor_ExecutedMessages_Call) RunAndReturn(run func(context.Context, map[ccipocr3.ChainSelector][]ccipocr3.SeqNumRange, primitives.ConfidenceLevel) (map[ccipocr3.ChainSelector][]ccipocr3.SeqNum, error)) *MockChainAccessor_ExecutedMessages_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllConfigsLegacy provides a mock function with given fields: ctx, destChainSelector, sourceChainSelectors
func (_m *MockChainAccessor) GetAllConfigsLegacy(ctx context.Context, destChainSelector ccipocr3.ChainSelector, sourceChainSelectors []ccipocr3.ChainSelector) (ccipocr3.ChainConfigSnapshot, map[ccipocr3.ChainSelector]ccipocr3.SourceChainConfig, error) {
	ret := _m.Called(ctx, destChainSelector, sourceChainSelectors)

	if len(ret) == 0 {
		panic("no return value specified for GetAllConfigsLegacy")
	}

	var r0 ccipocr3.ChainConfigSnapshot
	var r1 map[ccipocr3.ChainSelector]ccipocr3.SourceChainConfig
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, ccipocr3.ChainSelector, []ccipocr3.ChainSelector) (ccipocr3.ChainConfigSnapshot, map[ccipocr3.ChainSelector]ccipocr3.SourceChainConfig, error)); ok {
		return rf(ctx, destChainSelector, sourceChainSelectors)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ccipocr3.ChainSelector, []ccipocr3.ChainSelector) ccipocr3.ChainConfigSnapshot); ok {
		r0 = rf(ctx, destChainSelector, sourceChainSelectors)
	} else {
		r0 = ret.Get(0).(ccipocr3.ChainConfigSnapshot)
	}

	if rf, ok := ret.Get(1).(func(context.Context, ccipocr3.ChainSelector, []ccipocr3.ChainSelector) map[ccipocr3.ChainSelector]ccipocr3.SourceChainConfig); ok {
		r1 = rf(ctx, destChainSelector, sourceChainSelectors)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(map[ccipocr3.ChainSelector]ccipocr3.SourceChainConfig)
		}
	}

	if rf, ok := ret.Get(2).(func(context.Context, ccipocr3.ChainSelector, []ccipocr3.ChainSelector) error); ok {
		r2 = rf(ctx, destChainSelector, sourceChainSelectors)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// MockChainAccessor_GetAllConfigsLegacy_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllConfigsLegacy'
type MockChainAccessor_GetAllConfigsLegacy_Call struct {
	*mock.Call
}

// GetAllConfigsLegacy is a helper method to define mock.On call
//   - ctx context.Context
//   - destChainSelector ccipocr3.ChainSelector
//   - sourceChainSelectors []ccipocr3.ChainSelector
func (_e *MockChainAccessor_Expecter) GetAllConfigsLegacy(ctx interface{}, destChainSelector interface{}, sourceChainSelectors interface{}) *MockChainAccessor_GetAllConfigsLegacy_Call {
	return &MockChainAccessor_GetAllConfigsLegacy_Call{Call: _e.mock.On("GetAllConfigsLegacy", ctx, destChainSelector, sourceChainSelectors)}
}

func (_c *MockChainAccessor_GetAllConfigsLegacy_Call) Run(run func(ctx context.Context, destChainSelector ccipocr3.ChainSelector, sourceChainSelectors []ccipocr3.ChainSelector)) *MockChainAccessor_GetAllConfigsLegacy_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ccipocr3.ChainSelector), args[2].([]ccipocr3.ChainSelector))
	})
	return _c
}

func (_c *MockChainAccessor_GetAllConfigsLegacy_Call) Return(_a0 ccipocr3.ChainConfigSnapshot, _a1 map[ccipocr3.ChainSelector]ccipocr3.SourceChainConfig, _a2 error) *MockChainAccessor_GetAllConfigsLegacy_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *MockChainAccessor_GetAllConfigsLegacy_Call) RunAndReturn(run func(context.Context, ccipocr3.ChainSelector, []ccipocr3.ChainSelector) (ccipocr3.ChainConfigSnapshot, map[ccipocr3.ChainSelector]ccipocr3.SourceChainConfig, error)) *MockChainAccessor_GetAllConfigsLegacy_Call {
	_c.Call.Return(run)
	return _c
}

// GetChainFeeComponents provides a mock function with given fields: ctx
func (_m *MockChainAccessor) GetChainFeeComponents(ctx context.Context) (ccipocr3.ChainFeeComponents, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetChainFeeComponents")
	}

	var r0 ccipocr3.ChainFeeComponents
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (ccipocr3.ChainFeeComponents, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) ccipocr3.ChainFeeComponents); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(ccipocr3.ChainFeeComponents)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockChainAccessor_GetChainFeeComponents_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetChainFeeComponents'
type MockChainAccessor_GetChainFeeComponents_Call struct {
	*mock.Call
}

// GetChainFeeComponents is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockChainAccessor_Expecter) GetChainFeeComponents(ctx interface{}) *MockChainAccessor_GetChainFeeComponents_Call {
	return &MockChainAccessor_GetChainFeeComponents_Call{Call: _e.mock.On("GetChainFeeComponents", ctx)}
}

func (_c *MockChainAccessor_GetChainFeeComponents_Call) Run(run func(ctx context.Context)) *MockChainAccessor_GetChainFeeComponents_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockChainAccessor_GetChainFeeComponents_Call) Return(_a0 ccipocr3.ChainFeeComponents, _a1 error) *MockChainAccessor_GetChainFeeComponents_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockChainAccessor_GetChainFeeComponents_Call) RunAndReturn(run func(context.Context) (ccipocr3.ChainFeeComponents, error)) *MockChainAccessor_GetChainFeeComponents_Call {
	_c.Call.Return(run)
	return _c
}

// GetChainFeePriceUpdate provides a mock function with given fields: ctx, selectors
func (_m *MockChainAccessor) GetChainFeePriceUpdate(ctx context.Context, selectors []ccipocr3.ChainSelector) (map[ccipocr3.ChainSelector]ccipocr3.TimestampedUnixBig, error) {
	ret := _m.Called(ctx, selectors)

	if len(ret) == 0 {
		panic("no return value specified for GetChainFeePriceUpdate")
	}

	var r0 map[ccipocr3.ChainSelector]ccipocr3.TimestampedUnixBig
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []ccipocr3.ChainSelector) (map[ccipocr3.ChainSelector]ccipocr3.TimestampedUnixBig, error)); ok {
		return rf(ctx, selectors)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []ccipocr3.ChainSelector) map[ccipocr3.ChainSelector]ccipocr3.TimestampedUnixBig); ok {
		r0 = rf(ctx, selectors)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[ccipocr3.ChainSelector]ccipocr3.TimestampedUnixBig)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []ccipocr3.ChainSelector) error); ok {
		r1 = rf(ctx, selectors)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockChainAccessor_GetChainFeePriceUpdate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetChainFeePriceUpdate'
type MockChainAccessor_GetChainFeePriceUpdate_Call struct {
	*mock.Call
}

// GetChainFeePriceUpdate is a helper method to define mock.On call
//   - ctx context.Context
//   - selectors []ccipocr3.ChainSelector
func (_e *MockChainAccessor_Expecter) GetChainFeePriceUpdate(ctx interface{}, selectors interface{}) *MockChainAccessor_GetChainFeePriceUpdate_Call {
	return &MockChainAccessor_GetChainFeePriceUpdate_Call{Call: _e.mock.On("GetChainFeePriceUpdate", ctx, selectors)}
}

func (_c *MockChainAccessor_GetChainFeePriceUpdate_Call) Run(run func(ctx context.Context, selectors []ccipocr3.ChainSelector)) *MockChainAccessor_GetChainFeePriceUpdate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]ccipocr3.ChainSelector))
	})
	return _c
}

func (_c *MockChainAccessor_GetChainFeePriceUpdate_Call) Return(_a0 map[ccipocr3.ChainSelector]ccipocr3.TimestampedUnixBig, _a1 error) *MockChainAccessor_GetChainFeePriceUpdate_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockChainAccessor_GetChainFeePriceUpdate_Call) RunAndReturn(run func(context.Context, []ccipocr3.ChainSelector) (map[ccipocr3.ChainSelector]ccipocr3.TimestampedUnixBig, error)) *MockChainAccessor_GetChainFeePriceUpdate_Call {
	_c.Call.Return(run)
	return _c
}

// GetContractAddress provides a mock function with given fields: contractName
func (_m *MockChainAccessor) GetContractAddress(contractName string) ([]byte, error) {
	ret := _m.Called(contractName)

	if len(ret) == 0 {
		panic("no return value specified for GetContractAddress")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(string) ([]byte, error)); ok {
		return rf(contractName)
	}
	if rf, ok := ret.Get(0).(func(string) []byte); ok {
		r0 = rf(contractName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(contractName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockChainAccessor_GetContractAddress_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetContractAddress'
type MockChainAccessor_GetContractAddress_Call struct {
	*mock.Call
}

// GetContractAddress is a helper method to define mock.On call
//   - contractName string
func (_e *MockChainAccessor_Expecter) GetContractAddress(contractName interface{}) *MockChainAccessor_GetContractAddress_Call {
	return &MockChainAccessor_GetContractAddress_Call{Call: _e.mock.On("GetContractAddress", contractName)}
}

func (_c *MockChainAccessor_GetContractAddress_Call) Run(run func(contractName string)) *MockChainAccessor_GetContractAddress_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockChainAccessor_GetContractAddress_Call) Return(_a0 []byte, _a1 error) *MockChainAccessor_GetContractAddress_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockChainAccessor_GetContractAddress_Call) RunAndReturn(run func(string) ([]byte, error)) *MockChainAccessor_GetContractAddress_Call {
	_c.Call.Return(run)
	return _c
}

// GetExpectedNextSequenceNumber provides a mock function with given fields: ctx, dest
func (_m *MockChainAccessor) GetExpectedNextSequenceNumber(ctx context.Context, dest ccipocr3.ChainSelector) (ccipocr3.SeqNum, error) {
	ret := _m.Called(ctx, dest)

	if len(ret) == 0 {
		panic("no return value specified for GetExpectedNextSequenceNumber")
	}

	var r0 ccipocr3.SeqNum
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ccipocr3.ChainSelector) (ccipocr3.SeqNum, error)); ok {
		return rf(ctx, dest)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ccipocr3.ChainSelector) ccipocr3.SeqNum); ok {
		r0 = rf(ctx, dest)
	} else {
		r0 = ret.Get(0).(ccipocr3.SeqNum)
	}

	if rf, ok := ret.Get(1).(func(context.Context, ccipocr3.ChainSelector) error); ok {
		r1 = rf(ctx, dest)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockChainAccessor_GetExpectedNextSequenceNumber_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetExpectedNextSequenceNumber'
type MockChainAccessor_GetExpectedNextSequenceNumber_Call struct {
	*mock.Call
}

// GetExpectedNextSequenceNumber is a helper method to define mock.On call
//   - ctx context.Context
//   - dest ccipocr3.ChainSelector
func (_e *MockChainAccessor_Expecter) GetExpectedNextSequenceNumber(ctx interface{}, dest interface{}) *MockChainAccessor_GetExpectedNextSequenceNumber_Call {
	return &MockChainAccessor_GetExpectedNextSequenceNumber_Call{Call: _e.mock.On("GetExpectedNextSequenceNumber", ctx, dest)}
}

func (_c *MockChainAccessor_GetExpectedNextSequenceNumber_Call) Run(run func(ctx context.Context, dest ccipocr3.ChainSelector)) *MockChainAccessor_GetExpectedNextSequenceNumber_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ccipocr3.ChainSelector))
	})
	return _c
}

func (_c *MockChainAccessor_GetExpectedNextSequenceNumber_Call) Return(_a0 ccipocr3.SeqNum, _a1 error) *MockChainAccessor_GetExpectedNextSequenceNumber_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockChainAccessor_GetExpectedNextSequenceNumber_Call) RunAndReturn(run func(context.Context, ccipocr3.ChainSelector) (ccipocr3.SeqNum, error)) *MockChainAccessor_GetExpectedNextSequenceNumber_Call {
	_c.Call.Return(run)
	return _c
}

// GetFeeQuoterDestChainConfig provides a mock function with given fields: ctx, dest
func (_m *MockChainAccessor) GetFeeQuoterDestChainConfig(ctx context.Context, dest ccipocr3.ChainSelector) (ccipocr3.FeeQuoterDestChainConfig, error) {
	ret := _m.Called(ctx, dest)

	if len(ret) == 0 {
		panic("no return value specified for GetFeeQuoterDestChainConfig")
	}

	var r0 ccipocr3.FeeQuoterDestChainConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ccipocr3.ChainSelector) (ccipocr3.FeeQuoterDestChainConfig, error)); ok {
		return rf(ctx, dest)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ccipocr3.ChainSelector) ccipocr3.FeeQuoterDestChainConfig); ok {
		r0 = rf(ctx, dest)
	} else {
		r0 = ret.Get(0).(ccipocr3.FeeQuoterDestChainConfig)
	}

	if rf, ok := ret.Get(1).(func(context.Context, ccipocr3.ChainSelector) error); ok {
		r1 = rf(ctx, dest)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockChainAccessor_GetFeeQuoterDestChainConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeeQuoterDestChainConfig'
type MockChainAccessor_GetFeeQuoterDestChainConfig_Call struct {
	*mock.Call
}

// GetFeeQuoterDestChainConfig is a helper method to define mock.On call
//   - ctx context.Context
//   - dest ccipocr3.ChainSelector
func (_e *MockChainAccessor_Expecter) GetFeeQuoterDestChainConfig(ctx interface{}, dest interface{}) *MockChainAccessor_GetFeeQuoterDestChainConfig_Call {
	return &MockChainAccessor_GetFeeQuoterDestChainConfig_Call{Call: _e.mock.On("GetFeeQuoterDestChainConfig", ctx, dest)}
}

func (_c *MockChainAccessor_GetFeeQuoterDestChainConfig_Call) Run(run func(ctx context.Context, dest ccipocr3.ChainSelector)) *MockChainAccessor_GetFeeQuoterDestChainConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ccipocr3.ChainSelector))
	})
	return _c
}

func (_c *MockChainAccessor_GetFeeQuoterDestChainConfig_Call) Return(_a0 ccipocr3.FeeQuoterDestChainConfig, _a1 error) *MockChainAccessor_GetFeeQuoterDestChainConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockChainAccessor_GetFeeQuoterDestChainConfig_Call) RunAndReturn(run func(context.Context, ccipocr3.ChainSelector) (ccipocr3.FeeQuoterDestChainConfig, error)) *MockChainAccessor_GetFeeQuoterDestChainConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetFeeQuoterTokenUpdates provides a mock function with given fields: ctx, tokensBytes
func (_m *MockChainAccessor) GetFeeQuoterTokenUpdates(ctx context.Context, tokensBytes []ccipocr3.UnknownAddress) (map[ccipocr3.UnknownEncodedAddress]ccipocr3.TimestampedUnixBig, error) {
	ret := _m.Called(ctx, tokensBytes)

	if len(ret) == 0 {
		panic("no return value specified for GetFeeQuoterTokenUpdates")
	}

	var r0 map[ccipocr3.UnknownEncodedAddress]ccipocr3.TimestampedUnixBig
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []ccipocr3.UnknownAddress) (map[ccipocr3.UnknownEncodedAddress]ccipocr3.TimestampedUnixBig, error)); ok {
		return rf(ctx, tokensBytes)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []ccipocr3.UnknownAddress) map[ccipocr3.UnknownEncodedAddress]ccipocr3.TimestampedUnixBig); ok {
		r0 = rf(ctx, tokensBytes)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[ccipocr3.UnknownEncodedAddress]ccipocr3.TimestampedUnixBig)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []ccipocr3.UnknownAddress) error); ok {
		r1 = rf(ctx, tokensBytes)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockChainAccessor_GetFeeQuoterTokenUpdates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeeQuoterTokenUpdates'
type MockChainAccessor_GetFeeQuoterTokenUpdates_Call struct {
	*mock.Call
}

// GetFeeQuoterTokenUpdates is a helper method to define mock.On call
//   - ctx context.Context
//   - tokensBytes []ccipocr3.UnknownAddress
func (_e *MockChainAccessor_Expecter) GetFeeQuoterTokenUpdates(ctx interface{}, tokensBytes interface{}) *MockChainAccessor_GetFeeQuoterTokenUpdates_Call {
	return &MockChainAccessor_GetFeeQuoterTokenUpdates_Call{Call: _e.mock.On("GetFeeQuoterTokenUpdates", ctx, tokensBytes)}
}

func (_c *MockChainAccessor_GetFeeQuoterTokenUpdates_Call) Run(run func(ctx context.Context, tokensBytes []ccipocr3.UnknownAddress)) *MockChainAccessor_GetFeeQuoterTokenUpdates_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]ccipocr3.UnknownAddress))
	})
	return _c
}

func (_c *MockChainAccessor_GetFeeQuoterTokenUpdates_Call) Return(_a0 map[ccipocr3.UnknownEncodedAddress]ccipocr3.TimestampedUnixBig, _a1 error) *MockChainAccessor_GetFeeQuoterTokenUpdates_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockChainAccessor_GetFeeQuoterTokenUpdates_Call) RunAndReturn(run func(context.Context, []ccipocr3.UnknownAddress) (map[ccipocr3.UnknownEncodedAddress]ccipocr3.TimestampedUnixBig, error)) *MockChainAccessor_GetFeeQuoterTokenUpdates_Call {
	_c.Call.Return(run)
	return _c
}

// GetFeedPricesUSD provides a mock function with given fields: ctx, tokens, tokenInfo
func (_m *MockChainAccessor) GetFeedPricesUSD(ctx context.Context, tokens []ccipocr3.UnknownEncodedAddress, tokenInfo map[ccipocr3.UnknownEncodedAddress]ccipocr3.TokenInfo) (ccipocr3.TokenPriceMap, error) {
	ret := _m.Called(ctx, tokens, tokenInfo)

	if len(ret) == 0 {
		panic("no return value specified for GetFeedPricesUSD")
	}

	var r0 ccipocr3.TokenPriceMap
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []ccipocr3.UnknownEncodedAddress, map[ccipocr3.UnknownEncodedAddress]ccipocr3.TokenInfo) (ccipocr3.TokenPriceMap, error)); ok {
		return rf(ctx, tokens, tokenInfo)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []ccipocr3.UnknownEncodedAddress, map[ccipocr3.UnknownEncodedAddress]ccipocr3.TokenInfo) ccipocr3.TokenPriceMap); ok {
		r0 = rf(ctx, tokens, tokenInfo)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(ccipocr3.TokenPriceMap)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []ccipocr3.UnknownEncodedAddress, map[ccipocr3.UnknownEncodedAddress]ccipocr3.TokenInfo) error); ok {
		r1 = rf(ctx, tokens, tokenInfo)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockChainAccessor_GetFeedPricesUSD_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeedPricesUSD'
type MockChainAccessor_GetFeedPricesUSD_Call struct {
	*mock.Call
}

// GetFeedPricesUSD is a helper method to define mock.On call
//   - ctx context.Context
//   - tokens []ccipocr3.UnknownEncodedAddress
//   - tokenInfo map[ccipocr3.UnknownEncodedAddress]ccipocr3.TokenInfo
func (_e *MockChainAccessor_Expecter) GetFeedPricesUSD(ctx interface{}, tokens interface{}, tokenInfo interface{}) *MockChainAccessor_GetFeedPricesUSD_Call {
	return &MockChainAccessor_GetFeedPricesUSD_Call{Call: _e.mock.On("GetFeedPricesUSD", ctx, tokens, tokenInfo)}
}

func (_c *MockChainAccessor_GetFeedPricesUSD_Call) Run(run func(ctx context.Context, tokens []ccipocr3.UnknownEncodedAddress, tokenInfo map[ccipocr3.UnknownEncodedAddress]ccipocr3.TokenInfo)) *MockChainAccessor_GetFeedPricesUSD_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]ccipocr3.UnknownEncodedAddress), args[2].(map[ccipocr3.UnknownEncodedAddress]ccipocr3.TokenInfo))
	})
	return _c
}

func (_c *MockChainAccessor_GetFeedPricesUSD_Call) Return(_a0 ccipocr3.TokenPriceMap, _a1 error) *MockChainAccessor_GetFeedPricesUSD_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockChainAccessor_GetFeedPricesUSD_Call) RunAndReturn(run func(context.Context, []ccipocr3.UnknownEncodedAddress, map[ccipocr3.UnknownEncodedAddress]ccipocr3.TokenInfo) (ccipocr3.TokenPriceMap, error)) *MockChainAccessor_GetFeedPricesUSD_Call {
	_c.Call.Return(run)
	return _c
}

// GetLatestPriceSeqNr provides a mock function with given fields: ctx
func (_m *MockChainAccessor) GetLatestPriceSeqNr(ctx context.Context) (ccipocr3.SeqNum, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetLatestPriceSeqNr")
	}

	var r0 ccipocr3.SeqNum
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (ccipocr3.SeqNum, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) ccipocr3.SeqNum); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(ccipocr3.SeqNum)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockChainAccessor_GetLatestPriceSeqNr_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLatestPriceSeqNr'
type MockChainAccessor_GetLatestPriceSeqNr_Call struct {
	*mock.Call
}

// GetLatestPriceSeqNr is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockChainAccessor_Expecter) GetLatestPriceSeqNr(ctx interface{}) *MockChainAccessor_GetLatestPriceSeqNr_Call {
	return &MockChainAccessor_GetLatestPriceSeqNr_Call{Call: _e.mock.On("GetLatestPriceSeqNr", ctx)}
}

func (_c *MockChainAccessor_GetLatestPriceSeqNr_Call) Run(run func(ctx context.Context)) *MockChainAccessor_GetLatestPriceSeqNr_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockChainAccessor_GetLatestPriceSeqNr_Call) Return(_a0 ccipocr3.SeqNum, _a1 error) *MockChainAccessor_GetLatestPriceSeqNr_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockChainAccessor_GetLatestPriceSeqNr_Call) RunAndReturn(run func(context.Context) (ccipocr3.SeqNum, error)) *MockChainAccessor_GetLatestPriceSeqNr_Call {
	_c.Call.Return(run)
	return _c
}

// GetTokenPriceUSD provides a mock function with given fields: ctx, address
func (_m *MockChainAccessor) GetTokenPriceUSD(ctx context.Context, address ccipocr3.UnknownAddress) (ccipocr3.TimestampedUnixBig, error) {
	ret := _m.Called(ctx, address)

	if len(ret) == 0 {
		panic("no return value specified for GetTokenPriceUSD")
	}

	var r0 ccipocr3.TimestampedUnixBig
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ccipocr3.UnknownAddress) (ccipocr3.TimestampedUnixBig, error)); ok {
		return rf(ctx, address)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ccipocr3.UnknownAddress) ccipocr3.TimestampedUnixBig); ok {
		r0 = rf(ctx, address)
	} else {
		r0 = ret.Get(0).(ccipocr3.TimestampedUnixBig)
	}

	if rf, ok := ret.Get(1).(func(context.Context, ccipocr3.UnknownAddress) error); ok {
		r1 = rf(ctx, address)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockChainAccessor_GetTokenPriceUSD_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTokenPriceUSD'
type MockChainAccessor_GetTokenPriceUSD_Call struct {
	*mock.Call
}

// GetTokenPriceUSD is a helper method to define mock.On call
//   - ctx context.Context
//   - address ccipocr3.UnknownAddress
func (_e *MockChainAccessor_Expecter) GetTokenPriceUSD(ctx interface{}, address interface{}) *MockChainAccessor_GetTokenPriceUSD_Call {
	return &MockChainAccessor_GetTokenPriceUSD_Call{Call: _e.mock.On("GetTokenPriceUSD", ctx, address)}
}

func (_c *MockChainAccessor_GetTokenPriceUSD_Call) Run(run func(ctx context.Context, address ccipocr3.UnknownAddress)) *MockChainAccessor_GetTokenPriceUSD_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ccipocr3.UnknownAddress))
	})
	return _c
}

func (_c *MockChainAccessor_GetTokenPriceUSD_Call) Return(_a0 ccipocr3.TimestampedUnixBig, _a1 error) *MockChainAccessor_GetTokenPriceUSD_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockChainAccessor_GetTokenPriceUSD_Call) RunAndReturn(run func(context.Context, ccipocr3.UnknownAddress) (ccipocr3.TimestampedUnixBig, error)) *MockChainAccessor_GetTokenPriceUSD_Call {
	_c.Call.Return(run)
	return _c
}

// LatestMessageTo provides a mock function with given fields: ctx, dest
func (_m *MockChainAccessor) LatestMessageTo(ctx context.Context, dest ccipocr3.ChainSelector) (ccipocr3.SeqNum, error) {
	ret := _m.Called(ctx, dest)

	if len(ret) == 0 {
		panic("no return value specified for LatestMessageTo")
	}

	var r0 ccipocr3.SeqNum
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ccipocr3.ChainSelector) (ccipocr3.SeqNum, error)); ok {
		return rf(ctx, dest)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ccipocr3.ChainSelector) ccipocr3.SeqNum); ok {
		r0 = rf(ctx, dest)
	} else {
		r0 = ret.Get(0).(ccipocr3.SeqNum)
	}

	if rf, ok := ret.Get(1).(func(context.Context, ccipocr3.ChainSelector) error); ok {
		r1 = rf(ctx, dest)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockChainAccessor_LatestMessageTo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LatestMessageTo'
type MockChainAccessor_LatestMessageTo_Call struct {
	*mock.Call
}

// LatestMessageTo is a helper method to define mock.On call
//   - ctx context.Context
//   - dest ccipocr3.ChainSelector
func (_e *MockChainAccessor_Expecter) LatestMessageTo(ctx interface{}, dest interface{}) *MockChainAccessor_LatestMessageTo_Call {
	return &MockChainAccessor_LatestMessageTo_Call{Call: _e.mock.On("LatestMessageTo", ctx, dest)}
}

func (_c *MockChainAccessor_LatestMessageTo_Call) Run(run func(ctx context.Context, dest ccipocr3.ChainSelector)) *MockChainAccessor_LatestMessageTo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ccipocr3.ChainSelector))
	})
	return _c
}

func (_c *MockChainAccessor_LatestMessageTo_Call) Return(_a0 ccipocr3.SeqNum, _a1 error) *MockChainAccessor_LatestMessageTo_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockChainAccessor_LatestMessageTo_Call) RunAndReturn(run func(context.Context, ccipocr3.ChainSelector) (ccipocr3.SeqNum, error)) *MockChainAccessor_LatestMessageTo_Call {
	_c.Call.Return(run)
	return _c
}

// MessagesByTokenID provides a mock function with given fields: ctx, source, dest, tokens
func (_m *MockChainAccessor) MessagesByTokenID(ctx context.Context, source ccipocr3.ChainSelector, dest ccipocr3.ChainSelector, tokens map[ccipocr3.MessageTokenID]ccipocr3.RampTokenAmount) (map[ccipocr3.MessageTokenID]ccipocr3.Bytes, error) {
	ret := _m.Called(ctx, source, dest, tokens)

	if len(ret) == 0 {
		panic("no return value specified for MessagesByTokenID")
	}

	var r0 map[ccipocr3.MessageTokenID]ccipocr3.Bytes
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ccipocr3.ChainSelector, ccipocr3.ChainSelector, map[ccipocr3.MessageTokenID]ccipocr3.RampTokenAmount) (map[ccipocr3.MessageTokenID]ccipocr3.Bytes, error)); ok {
		return rf(ctx, source, dest, tokens)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ccipocr3.ChainSelector, ccipocr3.ChainSelector, map[ccipocr3.MessageTokenID]ccipocr3.RampTokenAmount) map[ccipocr3.MessageTokenID]ccipocr3.Bytes); ok {
		r0 = rf(ctx, source, dest, tokens)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[ccipocr3.MessageTokenID]ccipocr3.Bytes)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ccipocr3.ChainSelector, ccipocr3.ChainSelector, map[ccipocr3.MessageTokenID]ccipocr3.RampTokenAmount) error); ok {
		r1 = rf(ctx, source, dest, tokens)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockChainAccessor_MessagesByTokenID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MessagesByTokenID'
type MockChainAccessor_MessagesByTokenID_Call struct {
	*mock.Call
}

// MessagesByTokenID is a helper method to define mock.On call
//   - ctx context.Context
//   - source ccipocr3.ChainSelector
//   - dest ccipocr3.ChainSelector
//   - tokens map[ccipocr3.MessageTokenID]ccipocr3.RampTokenAmount
func (_e *MockChainAccessor_Expecter) MessagesByTokenID(ctx interface{}, source interface{}, dest interface{}, tokens interface{}) *MockChainAccessor_MessagesByTokenID_Call {
	return &MockChainAccessor_MessagesByTokenID_Call{Call: _e.mock.On("MessagesByTokenID", ctx, source, dest, tokens)}
}

func (_c *MockChainAccessor_MessagesByTokenID_Call) Run(run func(ctx context.Context, source ccipocr3.ChainSelector, dest ccipocr3.ChainSelector, tokens map[ccipocr3.MessageTokenID]ccipocr3.RampTokenAmount)) *MockChainAccessor_MessagesByTokenID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ccipocr3.ChainSelector), args[2].(ccipocr3.ChainSelector), args[3].(map[ccipocr3.MessageTokenID]ccipocr3.RampTokenAmount))
	})
	return _c
}

func (_c *MockChainAccessor_MessagesByTokenID_Call) Return(_a0 map[ccipocr3.MessageTokenID]ccipocr3.Bytes, _a1 error) *MockChainAccessor_MessagesByTokenID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockChainAccessor_MessagesByTokenID_Call) RunAndReturn(run func(context.Context, ccipocr3.ChainSelector, ccipocr3.ChainSelector, map[ccipocr3.MessageTokenID]ccipocr3.RampTokenAmount) (map[ccipocr3.MessageTokenID]ccipocr3.Bytes, error)) *MockChainAccessor_MessagesByTokenID_Call {
	_c.Call.Return(run)
	return _c
}

// MsgsBetweenSeqNums provides a mock function with given fields: ctx, dest, seqNumRange
func (_m *MockChainAccessor) MsgsBetweenSeqNums(ctx context.Context, dest ccipocr3.ChainSelector, seqNumRange ccipocr3.SeqNumRange) ([]ccipocr3.Message, error) {
	ret := _m.Called(ctx, dest, seqNumRange)

	if len(ret) == 0 {
		panic("no return value specified for MsgsBetweenSeqNums")
	}

	var r0 []ccipocr3.Message
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ccipocr3.ChainSelector, ccipocr3.SeqNumRange) ([]ccipocr3.Message, error)); ok {
		return rf(ctx, dest, seqNumRange)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ccipocr3.ChainSelector, ccipocr3.SeqNumRange) []ccipocr3.Message); ok {
		r0 = rf(ctx, dest, seqNumRange)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]ccipocr3.Message)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ccipocr3.ChainSelector, ccipocr3.SeqNumRange) error); ok {
		r1 = rf(ctx, dest, seqNumRange)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockChainAccessor_MsgsBetweenSeqNums_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MsgsBetweenSeqNums'
type MockChainAccessor_MsgsBetweenSeqNums_Call struct {
	*mock.Call
}

// MsgsBetweenSeqNums is a helper method to define mock.On call
//   - ctx context.Context
//   - dest ccipocr3.ChainSelector
//   - seqNumRange ccipocr3.SeqNumRange
func (_e *MockChainAccessor_Expecter) MsgsBetweenSeqNums(ctx interface{}, dest interface{}, seqNumRange interface{}) *MockChainAccessor_MsgsBetweenSeqNums_Call {
	return &MockChainAccessor_MsgsBetweenSeqNums_Call{Call: _e.mock.On("MsgsBetweenSeqNums", ctx, dest, seqNumRange)}
}

func (_c *MockChainAccessor_MsgsBetweenSeqNums_Call) Run(run func(ctx context.Context, dest ccipocr3.ChainSelector, seqNumRange ccipocr3.SeqNumRange)) *MockChainAccessor_MsgsBetweenSeqNums_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ccipocr3.ChainSelector), args[2].(ccipocr3.SeqNumRange))
	})
	return _c
}

func (_c *MockChainAccessor_MsgsBetweenSeqNums_Call) Return(_a0 []ccipocr3.Message, _a1 error) *MockChainAccessor_MsgsBetweenSeqNums_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockChainAccessor_MsgsBetweenSeqNums_Call) RunAndReturn(run func(context.Context, ccipocr3.ChainSelector, ccipocr3.SeqNumRange) ([]ccipocr3.Message, error)) *MockChainAccessor_MsgsBetweenSeqNums_Call {
	_c.Call.Return(run)
	return _c
}

// NextSeqNum provides a mock function with given fields: ctx, sources
func (_m *MockChainAccessor) NextSeqNum(ctx context.Context, sources []ccipocr3.ChainSelector) (map[ccipocr3.ChainSelector]ccipocr3.SeqNum, error) {
	ret := _m.Called(ctx, sources)

	if len(ret) == 0 {
		panic("no return value specified for NextSeqNum")
	}

	var r0 map[ccipocr3.ChainSelector]ccipocr3.SeqNum
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []ccipocr3.ChainSelector) (map[ccipocr3.ChainSelector]ccipocr3.SeqNum, error)); ok {
		return rf(ctx, sources)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []ccipocr3.ChainSelector) map[ccipocr3.ChainSelector]ccipocr3.SeqNum); ok {
		r0 = rf(ctx, sources)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[ccipocr3.ChainSelector]ccipocr3.SeqNum)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []ccipocr3.ChainSelector) error); ok {
		r1 = rf(ctx, sources)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockChainAccessor_NextSeqNum_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NextSeqNum'
type MockChainAccessor_NextSeqNum_Call struct {
	*mock.Call
}

// NextSeqNum is a helper method to define mock.On call
//   - ctx context.Context
//   - sources []ccipocr3.ChainSelector
func (_e *MockChainAccessor_Expecter) NextSeqNum(ctx interface{}, sources interface{}) *MockChainAccessor_NextSeqNum_Call {
	return &MockChainAccessor_NextSeqNum_Call{Call: _e.mock.On("NextSeqNum", ctx, sources)}
}

func (_c *MockChainAccessor_NextSeqNum_Call) Run(run func(ctx context.Context, sources []ccipocr3.ChainSelector)) *MockChainAccessor_NextSeqNum_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]ccipocr3.ChainSelector))
	})
	return _c
}

func (_c *MockChainAccessor_NextSeqNum_Call) Return(_a0 map[ccipocr3.ChainSelector]ccipocr3.SeqNum, _a1 error) *MockChainAccessor_NextSeqNum_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockChainAccessor_NextSeqNum_Call) RunAndReturn(run func(context.Context, []ccipocr3.ChainSelector) (map[ccipocr3.ChainSelector]ccipocr3.SeqNum, error)) *MockChainAccessor_NextSeqNum_Call {
	_c.Call.Return(run)
	return _c
}

// Nonces provides a mock function with given fields: ctx, addresses
func (_m *MockChainAccessor) Nonces(ctx context.Context, addresses map[ccipocr3.ChainSelector][]ccipocr3.UnknownEncodedAddress) (map[ccipocr3.ChainSelector]map[string]uint64, error) {
	ret := _m.Called(ctx, addresses)

	if len(ret) == 0 {
		panic("no return value specified for Nonces")
	}

	var r0 map[ccipocr3.ChainSelector]map[string]uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, map[ccipocr3.ChainSelector][]ccipocr3.UnknownEncodedAddress) (map[ccipocr3.ChainSelector]map[string]uint64, error)); ok {
		return rf(ctx, addresses)
	}
	if rf, ok := ret.Get(0).(func(context.Context, map[ccipocr3.ChainSelector][]ccipocr3.UnknownEncodedAddress) map[ccipocr3.ChainSelector]map[string]uint64); ok {
		r0 = rf(ctx, addresses)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[ccipocr3.ChainSelector]map[string]uint64)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, map[ccipocr3.ChainSelector][]ccipocr3.UnknownEncodedAddress) error); ok {
		r1 = rf(ctx, addresses)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockChainAccessor_Nonces_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Nonces'
type MockChainAccessor_Nonces_Call struct {
	*mock.Call
}

// Nonces is a helper method to define mock.On call
//   - ctx context.Context
//   - addresses map[ccipocr3.ChainSelector][]ccipocr3.UnknownEncodedAddress
func (_e *MockChainAccessor_Expecter) Nonces(ctx interface{}, addresses interface{}) *MockChainAccessor_Nonces_Call {
	return &MockChainAccessor_Nonces_Call{Call: _e.mock.On("Nonces", ctx, addresses)}
}

func (_c *MockChainAccessor_Nonces_Call) Run(run func(ctx context.Context, addresses map[ccipocr3.ChainSelector][]ccipocr3.UnknownEncodedAddress)) *MockChainAccessor_Nonces_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(map[ccipocr3.ChainSelector][]ccipocr3.UnknownEncodedAddress))
	})
	return _c
}

func (_c *MockChainAccessor_Nonces_Call) Return(_a0 map[ccipocr3.ChainSelector]map[string]uint64, _a1 error) *MockChainAccessor_Nonces_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockChainAccessor_Nonces_Call) RunAndReturn(run func(context.Context, map[ccipocr3.ChainSelector][]ccipocr3.UnknownEncodedAddress) (map[ccipocr3.ChainSelector]map[string]uint64, error)) *MockChainAccessor_Nonces_Call {
	_c.Call.Return(run)
	return _c
}

// Sync provides a mock function with given fields: ctx, contractName, contractAddress
func (_m *MockChainAccessor) Sync(ctx context.Context, contractName string, contractAddress ccipocr3.UnknownAddress) error {
	ret := _m.Called(ctx, contractName, contractAddress)

	if len(ret) == 0 {
		panic("no return value specified for Sync")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, ccipocr3.UnknownAddress) error); ok {
		r0 = rf(ctx, contractName, contractAddress)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockChainAccessor_Sync_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Sync'
type MockChainAccessor_Sync_Call struct {
	*mock.Call
}

// Sync is a helper method to define mock.On call
//   - ctx context.Context
//   - contractName string
//   - contractAddress ccipocr3.UnknownAddress
func (_e *MockChainAccessor_Expecter) Sync(ctx interface{}, contractName interface{}, contractAddress interface{}) *MockChainAccessor_Sync_Call {
	return &MockChainAccessor_Sync_Call{Call: _e.mock.On("Sync", ctx, contractName, contractAddress)}
}

func (_c *MockChainAccessor_Sync_Call) Run(run func(ctx context.Context, contractName string, contractAddress ccipocr3.UnknownAddress)) *MockChainAccessor_Sync_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(ccipocr3.UnknownAddress))
	})
	return _c
}

func (_c *MockChainAccessor_Sync_Call) Return(_a0 error) *MockChainAccessor_Sync_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockChainAccessor_Sync_Call) RunAndReturn(run func(context.Context, string, ccipocr3.UnknownAddress) error) *MockChainAccessor_Sync_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockChainAccessor creates a new instance of MockChainAccessor. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockChainAccessor(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockChainAccessor {
	mock := &MockChainAccessor{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
