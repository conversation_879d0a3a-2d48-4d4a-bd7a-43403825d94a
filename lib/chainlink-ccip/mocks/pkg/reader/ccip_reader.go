// Code generated by mockery v2.52.3. DO NOT EDIT.

package reader

import (
	context "context"

	ccipocr3 "github.com/smartcontractkit/chainlink-common/pkg/types/ccipocr3"

	mock "github.com/stretchr/testify/mock"

	primitives "github.com/smartcontractkit/chainlink-common/pkg/types/query/primitives"

	reader "github.com/smartcontractkit/chainlink-ccip/pkg/reader"

	time "time"

	types "github.com/smartcontractkit/chainlink-common/pkg/types"
)

// MockCCIPReader is an autogenerated mock type for the CCIPReader type
type MockCCIPReader struct {
	mock.Mock
}

type MockCCIPReader_Expecter struct {
	mock *mock.Mock
}

func (_m *MockCCIPReader) EXPECT() *MockCCIPReader_Expecter {
	return &MockCCIPReader_Expecter{mock: &_m.Mock}
}

// Close provides a mock function with no fields
func (_m *MockCCIPReader) Close() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockCCIPReader_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type MockCCIPReader_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *MockCCIPReader_Expecter) Close() *MockCCIPReader_Close_Call {
	return &MockCCIPReader_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *MockCCIPReader_Close_Call) Run(run func()) *MockCCIPReader_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockCCIPReader_Close_Call) Return(_a0 error) *MockCCIPReader_Close_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockCCIPReader_Close_Call) RunAndReturn(run func() error) *MockCCIPReader_Close_Call {
	_c.Call.Return(run)
	return _c
}

// CommitReportsGTETimestamp provides a mock function with given fields: ctx, ts, confidence, limit
func (_m *MockCCIPReader) CommitReportsGTETimestamp(ctx context.Context, ts time.Time, confidence primitives.ConfidenceLevel, limit int) ([]ccipocr3.CommitPluginReportWithMeta, error) {
	ret := _m.Called(ctx, ts, confidence, limit)

	if len(ret) == 0 {
		panic("no return value specified for CommitReportsGTETimestamp")
	}

	var r0 []ccipocr3.CommitPluginReportWithMeta
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, time.Time, primitives.ConfidenceLevel, int) ([]ccipocr3.CommitPluginReportWithMeta, error)); ok {
		return rf(ctx, ts, confidence, limit)
	}
	if rf, ok := ret.Get(0).(func(context.Context, time.Time, primitives.ConfidenceLevel, int) []ccipocr3.CommitPluginReportWithMeta); ok {
		r0 = rf(ctx, ts, confidence, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]ccipocr3.CommitPluginReportWithMeta)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, time.Time, primitives.ConfidenceLevel, int) error); ok {
		r1 = rf(ctx, ts, confidence, limit)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCCIPReader_CommitReportsGTETimestamp_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CommitReportsGTETimestamp'
type MockCCIPReader_CommitReportsGTETimestamp_Call struct {
	*mock.Call
}

// CommitReportsGTETimestamp is a helper method to define mock.On call
//   - ctx context.Context
//   - ts time.Time
//   - confidence primitives.ConfidenceLevel
//   - limit int
func (_e *MockCCIPReader_Expecter) CommitReportsGTETimestamp(ctx interface{}, ts interface{}, confidence interface{}, limit interface{}) *MockCCIPReader_CommitReportsGTETimestamp_Call {
	return &MockCCIPReader_CommitReportsGTETimestamp_Call{Call: _e.mock.On("CommitReportsGTETimestamp", ctx, ts, confidence, limit)}
}

func (_c *MockCCIPReader_CommitReportsGTETimestamp_Call) Run(run func(ctx context.Context, ts time.Time, confidence primitives.ConfidenceLevel, limit int)) *MockCCIPReader_CommitReportsGTETimestamp_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(time.Time), args[2].(primitives.ConfidenceLevel), args[3].(int))
	})
	return _c
}

func (_c *MockCCIPReader_CommitReportsGTETimestamp_Call) Return(_a0 []ccipocr3.CommitPluginReportWithMeta, _a1 error) *MockCCIPReader_CommitReportsGTETimestamp_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockCCIPReader_CommitReportsGTETimestamp_Call) RunAndReturn(run func(context.Context, time.Time, primitives.ConfidenceLevel, int) ([]ccipocr3.CommitPluginReportWithMeta, error)) *MockCCIPReader_CommitReportsGTETimestamp_Call {
	_c.Call.Return(run)
	return _c
}

// DiscoverContracts provides a mock function with given fields: ctx, supportedChains, allChains
func (_m *MockCCIPReader) DiscoverContracts(ctx context.Context, supportedChains []ccipocr3.ChainSelector, allChains []ccipocr3.ChainSelector) (reader.ContractAddresses, error) {
	ret := _m.Called(ctx, supportedChains, allChains)

	if len(ret) == 0 {
		panic("no return value specified for DiscoverContracts")
	}

	var r0 reader.ContractAddresses
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []ccipocr3.ChainSelector, []ccipocr3.ChainSelector) (reader.ContractAddresses, error)); ok {
		return rf(ctx, supportedChains, allChains)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []ccipocr3.ChainSelector, []ccipocr3.ChainSelector) reader.ContractAddresses); ok {
		r0 = rf(ctx, supportedChains, allChains)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(reader.ContractAddresses)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []ccipocr3.ChainSelector, []ccipocr3.ChainSelector) error); ok {
		r1 = rf(ctx, supportedChains, allChains)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCCIPReader_DiscoverContracts_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DiscoverContracts'
type MockCCIPReader_DiscoverContracts_Call struct {
	*mock.Call
}

// DiscoverContracts is a helper method to define mock.On call
//   - ctx context.Context
//   - supportedChains []ccipocr3.ChainSelector
//   - allChains []ccipocr3.ChainSelector
func (_e *MockCCIPReader_Expecter) DiscoverContracts(ctx interface{}, supportedChains interface{}, allChains interface{}) *MockCCIPReader_DiscoverContracts_Call {
	return &MockCCIPReader_DiscoverContracts_Call{Call: _e.mock.On("DiscoverContracts", ctx, supportedChains, allChains)}
}

func (_c *MockCCIPReader_DiscoverContracts_Call) Run(run func(ctx context.Context, supportedChains []ccipocr3.ChainSelector, allChains []ccipocr3.ChainSelector)) *MockCCIPReader_DiscoverContracts_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]ccipocr3.ChainSelector), args[2].([]ccipocr3.ChainSelector))
	})
	return _c
}

func (_c *MockCCIPReader_DiscoverContracts_Call) Return(_a0 reader.ContractAddresses, _a1 error) *MockCCIPReader_DiscoverContracts_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockCCIPReader_DiscoverContracts_Call) RunAndReturn(run func(context.Context, []ccipocr3.ChainSelector, []ccipocr3.ChainSelector) (reader.ContractAddresses, error)) *MockCCIPReader_DiscoverContracts_Call {
	_c.Call.Return(run)
	return _c
}

// ExecutedMessages provides a mock function with given fields: ctx, rangesPerChain, confidence
func (_m *MockCCIPReader) ExecutedMessages(ctx context.Context, rangesPerChain map[ccipocr3.ChainSelector][]ccipocr3.SeqNumRange, confidence primitives.ConfidenceLevel) (map[ccipocr3.ChainSelector][]ccipocr3.SeqNum, error) {
	ret := _m.Called(ctx, rangesPerChain, confidence)

	if len(ret) == 0 {
		panic("no return value specified for ExecutedMessages")
	}

	var r0 map[ccipocr3.ChainSelector][]ccipocr3.SeqNum
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, map[ccipocr3.ChainSelector][]ccipocr3.SeqNumRange, primitives.ConfidenceLevel) (map[ccipocr3.ChainSelector][]ccipocr3.SeqNum, error)); ok {
		return rf(ctx, rangesPerChain, confidence)
	}
	if rf, ok := ret.Get(0).(func(context.Context, map[ccipocr3.ChainSelector][]ccipocr3.SeqNumRange, primitives.ConfidenceLevel) map[ccipocr3.ChainSelector][]ccipocr3.SeqNum); ok {
		r0 = rf(ctx, rangesPerChain, confidence)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[ccipocr3.ChainSelector][]ccipocr3.SeqNum)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, map[ccipocr3.ChainSelector][]ccipocr3.SeqNumRange, primitives.ConfidenceLevel) error); ok {
		r1 = rf(ctx, rangesPerChain, confidence)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCCIPReader_ExecutedMessages_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ExecutedMessages'
type MockCCIPReader_ExecutedMessages_Call struct {
	*mock.Call
}

// ExecutedMessages is a helper method to define mock.On call
//   - ctx context.Context
//   - rangesPerChain map[ccipocr3.ChainSelector][]ccipocr3.SeqNumRange
//   - confidence primitives.ConfidenceLevel
func (_e *MockCCIPReader_Expecter) ExecutedMessages(ctx interface{}, rangesPerChain interface{}, confidence interface{}) *MockCCIPReader_ExecutedMessages_Call {
	return &MockCCIPReader_ExecutedMessages_Call{Call: _e.mock.On("ExecutedMessages", ctx, rangesPerChain, confidence)}
}

func (_c *MockCCIPReader_ExecutedMessages_Call) Run(run func(ctx context.Context, rangesPerChain map[ccipocr3.ChainSelector][]ccipocr3.SeqNumRange, confidence primitives.ConfidenceLevel)) *MockCCIPReader_ExecutedMessages_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(map[ccipocr3.ChainSelector][]ccipocr3.SeqNumRange), args[2].(primitives.ConfidenceLevel))
	})
	return _c
}

func (_c *MockCCIPReader_ExecutedMessages_Call) Return(_a0 map[ccipocr3.ChainSelector][]ccipocr3.SeqNum, _a1 error) *MockCCIPReader_ExecutedMessages_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockCCIPReader_ExecutedMessages_Call) RunAndReturn(run func(context.Context, map[ccipocr3.ChainSelector][]ccipocr3.SeqNumRange, primitives.ConfidenceLevel) (map[ccipocr3.ChainSelector][]ccipocr3.SeqNum, error)) *MockCCIPReader_ExecutedMessages_Call {
	_c.Call.Return(run)
	return _c
}

// GetChainFeePriceUpdate provides a mock function with given fields: ctx, selectors
func (_m *MockCCIPReader) GetChainFeePriceUpdate(ctx context.Context, selectors []ccipocr3.ChainSelector) map[ccipocr3.ChainSelector]ccipocr3.TimestampedBig {
	ret := _m.Called(ctx, selectors)

	if len(ret) == 0 {
		panic("no return value specified for GetChainFeePriceUpdate")
	}

	var r0 map[ccipocr3.ChainSelector]ccipocr3.TimestampedBig
	if rf, ok := ret.Get(0).(func(context.Context, []ccipocr3.ChainSelector) map[ccipocr3.ChainSelector]ccipocr3.TimestampedBig); ok {
		r0 = rf(ctx, selectors)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[ccipocr3.ChainSelector]ccipocr3.TimestampedBig)
		}
	}

	return r0
}

// MockCCIPReader_GetChainFeePriceUpdate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetChainFeePriceUpdate'
type MockCCIPReader_GetChainFeePriceUpdate_Call struct {
	*mock.Call
}

// GetChainFeePriceUpdate is a helper method to define mock.On call
//   - ctx context.Context
//   - selectors []ccipocr3.ChainSelector
func (_e *MockCCIPReader_Expecter) GetChainFeePriceUpdate(ctx interface{}, selectors interface{}) *MockCCIPReader_GetChainFeePriceUpdate_Call {
	return &MockCCIPReader_GetChainFeePriceUpdate_Call{Call: _e.mock.On("GetChainFeePriceUpdate", ctx, selectors)}
}

func (_c *MockCCIPReader_GetChainFeePriceUpdate_Call) Run(run func(ctx context.Context, selectors []ccipocr3.ChainSelector)) *MockCCIPReader_GetChainFeePriceUpdate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]ccipocr3.ChainSelector))
	})
	return _c
}

func (_c *MockCCIPReader_GetChainFeePriceUpdate_Call) Return(_a0 map[ccipocr3.ChainSelector]ccipocr3.TimestampedBig) *MockCCIPReader_GetChainFeePriceUpdate_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockCCIPReader_GetChainFeePriceUpdate_Call) RunAndReturn(run func(context.Context, []ccipocr3.ChainSelector) map[ccipocr3.ChainSelector]ccipocr3.TimestampedBig) *MockCCIPReader_GetChainFeePriceUpdate_Call {
	_c.Call.Return(run)
	return _c
}

// GetChainsFeeComponents provides a mock function with given fields: ctx, chains
func (_m *MockCCIPReader) GetChainsFeeComponents(ctx context.Context, chains []ccipocr3.ChainSelector) map[ccipocr3.ChainSelector]types.ChainFeeComponents {
	ret := _m.Called(ctx, chains)

	if len(ret) == 0 {
		panic("no return value specified for GetChainsFeeComponents")
	}

	var r0 map[ccipocr3.ChainSelector]types.ChainFeeComponents
	if rf, ok := ret.Get(0).(func(context.Context, []ccipocr3.ChainSelector) map[ccipocr3.ChainSelector]types.ChainFeeComponents); ok {
		r0 = rf(ctx, chains)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[ccipocr3.ChainSelector]types.ChainFeeComponents)
		}
	}

	return r0
}

// MockCCIPReader_GetChainsFeeComponents_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetChainsFeeComponents'
type MockCCIPReader_GetChainsFeeComponents_Call struct {
	*mock.Call
}

// GetChainsFeeComponents is a helper method to define mock.On call
//   - ctx context.Context
//   - chains []ccipocr3.ChainSelector
func (_e *MockCCIPReader_Expecter) GetChainsFeeComponents(ctx interface{}, chains interface{}) *MockCCIPReader_GetChainsFeeComponents_Call {
	return &MockCCIPReader_GetChainsFeeComponents_Call{Call: _e.mock.On("GetChainsFeeComponents", ctx, chains)}
}

func (_c *MockCCIPReader_GetChainsFeeComponents_Call) Run(run func(ctx context.Context, chains []ccipocr3.ChainSelector)) *MockCCIPReader_GetChainsFeeComponents_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]ccipocr3.ChainSelector))
	})
	return _c
}

func (_c *MockCCIPReader_GetChainsFeeComponents_Call) Return(_a0 map[ccipocr3.ChainSelector]types.ChainFeeComponents) *MockCCIPReader_GetChainsFeeComponents_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockCCIPReader_GetChainsFeeComponents_Call) RunAndReturn(run func(context.Context, []ccipocr3.ChainSelector) map[ccipocr3.ChainSelector]types.ChainFeeComponents) *MockCCIPReader_GetChainsFeeComponents_Call {
	_c.Call.Return(run)
	return _c
}

// GetContractAddress provides a mock function with given fields: contractName, chain
func (_m *MockCCIPReader) GetContractAddress(contractName string, chain ccipocr3.ChainSelector) ([]byte, error) {
	ret := _m.Called(contractName, chain)

	if len(ret) == 0 {
		panic("no return value specified for GetContractAddress")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(string, ccipocr3.ChainSelector) ([]byte, error)); ok {
		return rf(contractName, chain)
	}
	if rf, ok := ret.Get(0).(func(string, ccipocr3.ChainSelector) []byte); ok {
		r0 = rf(contractName, chain)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(string, ccipocr3.ChainSelector) error); ok {
		r1 = rf(contractName, chain)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCCIPReader_GetContractAddress_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetContractAddress'
type MockCCIPReader_GetContractAddress_Call struct {
	*mock.Call
}

// GetContractAddress is a helper method to define mock.On call
//   - contractName string
//   - chain ccipocr3.ChainSelector
func (_e *MockCCIPReader_Expecter) GetContractAddress(contractName interface{}, chain interface{}) *MockCCIPReader_GetContractAddress_Call {
	return &MockCCIPReader_GetContractAddress_Call{Call: _e.mock.On("GetContractAddress", contractName, chain)}
}

func (_c *MockCCIPReader_GetContractAddress_Call) Run(run func(contractName string, chain ccipocr3.ChainSelector)) *MockCCIPReader_GetContractAddress_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(ccipocr3.ChainSelector))
	})
	return _c
}

func (_c *MockCCIPReader_GetContractAddress_Call) Return(_a0 []byte, _a1 error) *MockCCIPReader_GetContractAddress_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockCCIPReader_GetContractAddress_Call) RunAndReturn(run func(string, ccipocr3.ChainSelector) ([]byte, error)) *MockCCIPReader_GetContractAddress_Call {
	_c.Call.Return(run)
	return _c
}

// GetDestChainFeeComponents provides a mock function with given fields: ctx
func (_m *MockCCIPReader) GetDestChainFeeComponents(ctx context.Context) (types.ChainFeeComponents, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetDestChainFeeComponents")
	}

	var r0 types.ChainFeeComponents
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (types.ChainFeeComponents, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) types.ChainFeeComponents); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(types.ChainFeeComponents)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCCIPReader_GetDestChainFeeComponents_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDestChainFeeComponents'
type MockCCIPReader_GetDestChainFeeComponents_Call struct {
	*mock.Call
}

// GetDestChainFeeComponents is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockCCIPReader_Expecter) GetDestChainFeeComponents(ctx interface{}) *MockCCIPReader_GetDestChainFeeComponents_Call {
	return &MockCCIPReader_GetDestChainFeeComponents_Call{Call: _e.mock.On("GetDestChainFeeComponents", ctx)}
}

func (_c *MockCCIPReader_GetDestChainFeeComponents_Call) Run(run func(ctx context.Context)) *MockCCIPReader_GetDestChainFeeComponents_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockCCIPReader_GetDestChainFeeComponents_Call) Return(_a0 types.ChainFeeComponents, _a1 error) *MockCCIPReader_GetDestChainFeeComponents_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockCCIPReader_GetDestChainFeeComponents_Call) RunAndReturn(run func(context.Context) (types.ChainFeeComponents, error)) *MockCCIPReader_GetDestChainFeeComponents_Call {
	_c.Call.Return(run)
	return _c
}

// GetExpectedNextSequenceNumber provides a mock function with given fields: ctx, sourceChainSelector
func (_m *MockCCIPReader) GetExpectedNextSequenceNumber(ctx context.Context, sourceChainSelector ccipocr3.ChainSelector) (ccipocr3.SeqNum, error) {
	ret := _m.Called(ctx, sourceChainSelector)

	if len(ret) == 0 {
		panic("no return value specified for GetExpectedNextSequenceNumber")
	}

	var r0 ccipocr3.SeqNum
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ccipocr3.ChainSelector) (ccipocr3.SeqNum, error)); ok {
		return rf(ctx, sourceChainSelector)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ccipocr3.ChainSelector) ccipocr3.SeqNum); ok {
		r0 = rf(ctx, sourceChainSelector)
	} else {
		r0 = ret.Get(0).(ccipocr3.SeqNum)
	}

	if rf, ok := ret.Get(1).(func(context.Context, ccipocr3.ChainSelector) error); ok {
		r1 = rf(ctx, sourceChainSelector)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCCIPReader_GetExpectedNextSequenceNumber_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetExpectedNextSequenceNumber'
type MockCCIPReader_GetExpectedNextSequenceNumber_Call struct {
	*mock.Call
}

// GetExpectedNextSequenceNumber is a helper method to define mock.On call
//   - ctx context.Context
//   - sourceChainSelector ccipocr3.ChainSelector
func (_e *MockCCIPReader_Expecter) GetExpectedNextSequenceNumber(ctx interface{}, sourceChainSelector interface{}) *MockCCIPReader_GetExpectedNextSequenceNumber_Call {
	return &MockCCIPReader_GetExpectedNextSequenceNumber_Call{Call: _e.mock.On("GetExpectedNextSequenceNumber", ctx, sourceChainSelector)}
}

func (_c *MockCCIPReader_GetExpectedNextSequenceNumber_Call) Run(run func(ctx context.Context, sourceChainSelector ccipocr3.ChainSelector)) *MockCCIPReader_GetExpectedNextSequenceNumber_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ccipocr3.ChainSelector))
	})
	return _c
}

func (_c *MockCCIPReader_GetExpectedNextSequenceNumber_Call) Return(_a0 ccipocr3.SeqNum, _a1 error) *MockCCIPReader_GetExpectedNextSequenceNumber_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockCCIPReader_GetExpectedNextSequenceNumber_Call) RunAndReturn(run func(context.Context, ccipocr3.ChainSelector) (ccipocr3.SeqNum, error)) *MockCCIPReader_GetExpectedNextSequenceNumber_Call {
	_c.Call.Return(run)
	return _c
}

// GetLatestPriceSeqNr provides a mock function with given fields: ctx
func (_m *MockCCIPReader) GetLatestPriceSeqNr(ctx context.Context) (uint64, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetLatestPriceSeqNr")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (uint64, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) uint64); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCCIPReader_GetLatestPriceSeqNr_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLatestPriceSeqNr'
type MockCCIPReader_GetLatestPriceSeqNr_Call struct {
	*mock.Call
}

// GetLatestPriceSeqNr is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockCCIPReader_Expecter) GetLatestPriceSeqNr(ctx interface{}) *MockCCIPReader_GetLatestPriceSeqNr_Call {
	return &MockCCIPReader_GetLatestPriceSeqNr_Call{Call: _e.mock.On("GetLatestPriceSeqNr", ctx)}
}

func (_c *MockCCIPReader_GetLatestPriceSeqNr_Call) Run(run func(ctx context.Context)) *MockCCIPReader_GetLatestPriceSeqNr_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockCCIPReader_GetLatestPriceSeqNr_Call) Return(_a0 uint64, _a1 error) *MockCCIPReader_GetLatestPriceSeqNr_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockCCIPReader_GetLatestPriceSeqNr_Call) RunAndReturn(run func(context.Context) (uint64, error)) *MockCCIPReader_GetLatestPriceSeqNr_Call {
	_c.Call.Return(run)
	return _c
}

// GetOffRampConfigDigest provides a mock function with given fields: ctx, pluginType
func (_m *MockCCIPReader) GetOffRampConfigDigest(ctx context.Context, pluginType uint8) ([32]byte, error) {
	ret := _m.Called(ctx, pluginType)

	if len(ret) == 0 {
		panic("no return value specified for GetOffRampConfigDigest")
	}

	var r0 [32]byte
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint8) ([32]byte, error)); ok {
		return rf(ctx, pluginType)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint8) [32]byte); ok {
		r0 = rf(ctx, pluginType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([32]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint8) error); ok {
		r1 = rf(ctx, pluginType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCCIPReader_GetOffRampConfigDigest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOffRampConfigDigest'
type MockCCIPReader_GetOffRampConfigDigest_Call struct {
	*mock.Call
}

// GetOffRampConfigDigest is a helper method to define mock.On call
//   - ctx context.Context
//   - pluginType uint8
func (_e *MockCCIPReader_Expecter) GetOffRampConfigDigest(ctx interface{}, pluginType interface{}) *MockCCIPReader_GetOffRampConfigDigest_Call {
	return &MockCCIPReader_GetOffRampConfigDigest_Call{Call: _e.mock.On("GetOffRampConfigDigest", ctx, pluginType)}
}

func (_c *MockCCIPReader_GetOffRampConfigDigest_Call) Run(run func(ctx context.Context, pluginType uint8)) *MockCCIPReader_GetOffRampConfigDigest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint8))
	})
	return _c
}

func (_c *MockCCIPReader_GetOffRampConfigDigest_Call) Return(_a0 [32]byte, _a1 error) *MockCCIPReader_GetOffRampConfigDigest_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockCCIPReader_GetOffRampConfigDigest_Call) RunAndReturn(run func(context.Context, uint8) ([32]byte, error)) *MockCCIPReader_GetOffRampConfigDigest_Call {
	_c.Call.Return(run)
	return _c
}

// GetOffRampSourceChainsConfig provides a mock function with given fields: ctx, sourceChains
func (_m *MockCCIPReader) GetOffRampSourceChainsConfig(ctx context.Context, sourceChains []ccipocr3.ChainSelector) (map[ccipocr3.ChainSelector]reader.StaticSourceChainConfig, error) {
	ret := _m.Called(ctx, sourceChains)

	if len(ret) == 0 {
		panic("no return value specified for GetOffRampSourceChainsConfig")
	}

	var r0 map[ccipocr3.ChainSelector]reader.StaticSourceChainConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []ccipocr3.ChainSelector) (map[ccipocr3.ChainSelector]reader.StaticSourceChainConfig, error)); ok {
		return rf(ctx, sourceChains)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []ccipocr3.ChainSelector) map[ccipocr3.ChainSelector]reader.StaticSourceChainConfig); ok {
		r0 = rf(ctx, sourceChains)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[ccipocr3.ChainSelector]reader.StaticSourceChainConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []ccipocr3.ChainSelector) error); ok {
		r1 = rf(ctx, sourceChains)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCCIPReader_GetOffRampSourceChainsConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOffRampSourceChainsConfig'
type MockCCIPReader_GetOffRampSourceChainsConfig_Call struct {
	*mock.Call
}

// GetOffRampSourceChainsConfig is a helper method to define mock.On call
//   - ctx context.Context
//   - sourceChains []ccipocr3.ChainSelector
func (_e *MockCCIPReader_Expecter) GetOffRampSourceChainsConfig(ctx interface{}, sourceChains interface{}) *MockCCIPReader_GetOffRampSourceChainsConfig_Call {
	return &MockCCIPReader_GetOffRampSourceChainsConfig_Call{Call: _e.mock.On("GetOffRampSourceChainsConfig", ctx, sourceChains)}
}

func (_c *MockCCIPReader_GetOffRampSourceChainsConfig_Call) Run(run func(ctx context.Context, sourceChains []ccipocr3.ChainSelector)) *MockCCIPReader_GetOffRampSourceChainsConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]ccipocr3.ChainSelector))
	})
	return _c
}

func (_c *MockCCIPReader_GetOffRampSourceChainsConfig_Call) Return(_a0 map[ccipocr3.ChainSelector]reader.StaticSourceChainConfig, _a1 error) *MockCCIPReader_GetOffRampSourceChainsConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockCCIPReader_GetOffRampSourceChainsConfig_Call) RunAndReturn(run func(context.Context, []ccipocr3.ChainSelector) (map[ccipocr3.ChainSelector]reader.StaticSourceChainConfig, error)) *MockCCIPReader_GetOffRampSourceChainsConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetRMNRemoteConfig provides a mock function with given fields: ctx
func (_m *MockCCIPReader) GetRMNRemoteConfig(ctx context.Context) (ccipocr3.RemoteConfig, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetRMNRemoteConfig")
	}

	var r0 ccipocr3.RemoteConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (ccipocr3.RemoteConfig, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) ccipocr3.RemoteConfig); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(ccipocr3.RemoteConfig)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCCIPReader_GetRMNRemoteConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRMNRemoteConfig'
type MockCCIPReader_GetRMNRemoteConfig_Call struct {
	*mock.Call
}

// GetRMNRemoteConfig is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockCCIPReader_Expecter) GetRMNRemoteConfig(ctx interface{}) *MockCCIPReader_GetRMNRemoteConfig_Call {
	return &MockCCIPReader_GetRMNRemoteConfig_Call{Call: _e.mock.On("GetRMNRemoteConfig", ctx)}
}

func (_c *MockCCIPReader_GetRMNRemoteConfig_Call) Run(run func(ctx context.Context)) *MockCCIPReader_GetRMNRemoteConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockCCIPReader_GetRMNRemoteConfig_Call) Return(_a0 ccipocr3.RemoteConfig, _a1 error) *MockCCIPReader_GetRMNRemoteConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockCCIPReader_GetRMNRemoteConfig_Call) RunAndReturn(run func(context.Context) (ccipocr3.RemoteConfig, error)) *MockCCIPReader_GetRMNRemoteConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetRmnCurseInfo provides a mock function with given fields: ctx
func (_m *MockCCIPReader) GetRmnCurseInfo(ctx context.Context) (ccipocr3.CurseInfo, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetRmnCurseInfo")
	}

	var r0 ccipocr3.CurseInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (ccipocr3.CurseInfo, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) ccipocr3.CurseInfo); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(ccipocr3.CurseInfo)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCCIPReader_GetRmnCurseInfo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRmnCurseInfo'
type MockCCIPReader_GetRmnCurseInfo_Call struct {
	*mock.Call
}

// GetRmnCurseInfo is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockCCIPReader_Expecter) GetRmnCurseInfo(ctx interface{}) *MockCCIPReader_GetRmnCurseInfo_Call {
	return &MockCCIPReader_GetRmnCurseInfo_Call{Call: _e.mock.On("GetRmnCurseInfo", ctx)}
}

func (_c *MockCCIPReader_GetRmnCurseInfo_Call) Run(run func(ctx context.Context)) *MockCCIPReader_GetRmnCurseInfo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockCCIPReader_GetRmnCurseInfo_Call) Return(_a0 ccipocr3.CurseInfo, _a1 error) *MockCCIPReader_GetRmnCurseInfo_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockCCIPReader_GetRmnCurseInfo_Call) RunAndReturn(run func(context.Context) (ccipocr3.CurseInfo, error)) *MockCCIPReader_GetRmnCurseInfo_Call {
	_c.Call.Return(run)
	return _c
}

// GetWrappedNativeTokenPriceUSD provides a mock function with given fields: ctx, selectors
func (_m *MockCCIPReader) GetWrappedNativeTokenPriceUSD(ctx context.Context, selectors []ccipocr3.ChainSelector) map[ccipocr3.ChainSelector]ccipocr3.BigInt {
	ret := _m.Called(ctx, selectors)

	if len(ret) == 0 {
		panic("no return value specified for GetWrappedNativeTokenPriceUSD")
	}

	var r0 map[ccipocr3.ChainSelector]ccipocr3.BigInt
	if rf, ok := ret.Get(0).(func(context.Context, []ccipocr3.ChainSelector) map[ccipocr3.ChainSelector]ccipocr3.BigInt); ok {
		r0 = rf(ctx, selectors)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[ccipocr3.ChainSelector]ccipocr3.BigInt)
		}
	}

	return r0
}

// MockCCIPReader_GetWrappedNativeTokenPriceUSD_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetWrappedNativeTokenPriceUSD'
type MockCCIPReader_GetWrappedNativeTokenPriceUSD_Call struct {
	*mock.Call
}

// GetWrappedNativeTokenPriceUSD is a helper method to define mock.On call
//   - ctx context.Context
//   - selectors []ccipocr3.ChainSelector
func (_e *MockCCIPReader_Expecter) GetWrappedNativeTokenPriceUSD(ctx interface{}, selectors interface{}) *MockCCIPReader_GetWrappedNativeTokenPriceUSD_Call {
	return &MockCCIPReader_GetWrappedNativeTokenPriceUSD_Call{Call: _e.mock.On("GetWrappedNativeTokenPriceUSD", ctx, selectors)}
}

func (_c *MockCCIPReader_GetWrappedNativeTokenPriceUSD_Call) Run(run func(ctx context.Context, selectors []ccipocr3.ChainSelector)) *MockCCIPReader_GetWrappedNativeTokenPriceUSD_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]ccipocr3.ChainSelector))
	})
	return _c
}

func (_c *MockCCIPReader_GetWrappedNativeTokenPriceUSD_Call) Return(_a0 map[ccipocr3.ChainSelector]ccipocr3.BigInt) *MockCCIPReader_GetWrappedNativeTokenPriceUSD_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockCCIPReader_GetWrappedNativeTokenPriceUSD_Call) RunAndReturn(run func(context.Context, []ccipocr3.ChainSelector) map[ccipocr3.ChainSelector]ccipocr3.BigInt) *MockCCIPReader_GetWrappedNativeTokenPriceUSD_Call {
	_c.Call.Return(run)
	return _c
}

// LatestMsgSeqNum provides a mock function with given fields: ctx, chain
func (_m *MockCCIPReader) LatestMsgSeqNum(ctx context.Context, chain ccipocr3.ChainSelector) (ccipocr3.SeqNum, error) {
	ret := _m.Called(ctx, chain)

	if len(ret) == 0 {
		panic("no return value specified for LatestMsgSeqNum")
	}

	var r0 ccipocr3.SeqNum
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ccipocr3.ChainSelector) (ccipocr3.SeqNum, error)); ok {
		return rf(ctx, chain)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ccipocr3.ChainSelector) ccipocr3.SeqNum); ok {
		r0 = rf(ctx, chain)
	} else {
		r0 = ret.Get(0).(ccipocr3.SeqNum)
	}

	if rf, ok := ret.Get(1).(func(context.Context, ccipocr3.ChainSelector) error); ok {
		r1 = rf(ctx, chain)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCCIPReader_LatestMsgSeqNum_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LatestMsgSeqNum'
type MockCCIPReader_LatestMsgSeqNum_Call struct {
	*mock.Call
}

// LatestMsgSeqNum is a helper method to define mock.On call
//   - ctx context.Context
//   - chain ccipocr3.ChainSelector
func (_e *MockCCIPReader_Expecter) LatestMsgSeqNum(ctx interface{}, chain interface{}) *MockCCIPReader_LatestMsgSeqNum_Call {
	return &MockCCIPReader_LatestMsgSeqNum_Call{Call: _e.mock.On("LatestMsgSeqNum", ctx, chain)}
}

func (_c *MockCCIPReader_LatestMsgSeqNum_Call) Run(run func(ctx context.Context, chain ccipocr3.ChainSelector)) *MockCCIPReader_LatestMsgSeqNum_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ccipocr3.ChainSelector))
	})
	return _c
}

func (_c *MockCCIPReader_LatestMsgSeqNum_Call) Return(_a0 ccipocr3.SeqNum, _a1 error) *MockCCIPReader_LatestMsgSeqNum_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockCCIPReader_LatestMsgSeqNum_Call) RunAndReturn(run func(context.Context, ccipocr3.ChainSelector) (ccipocr3.SeqNum, error)) *MockCCIPReader_LatestMsgSeqNum_Call {
	_c.Call.Return(run)
	return _c
}

// LinkPriceUSD provides a mock function with given fields: ctx
func (_m *MockCCIPReader) LinkPriceUSD(ctx context.Context) (ccipocr3.BigInt, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for LinkPriceUSD")
	}

	var r0 ccipocr3.BigInt
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (ccipocr3.BigInt, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) ccipocr3.BigInt); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(ccipocr3.BigInt)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCCIPReader_LinkPriceUSD_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LinkPriceUSD'
type MockCCIPReader_LinkPriceUSD_Call struct {
	*mock.Call
}

// LinkPriceUSD is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockCCIPReader_Expecter) LinkPriceUSD(ctx interface{}) *MockCCIPReader_LinkPriceUSD_Call {
	return &MockCCIPReader_LinkPriceUSD_Call{Call: _e.mock.On("LinkPriceUSD", ctx)}
}

func (_c *MockCCIPReader_LinkPriceUSD_Call) Run(run func(ctx context.Context)) *MockCCIPReader_LinkPriceUSD_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockCCIPReader_LinkPriceUSD_Call) Return(_a0 ccipocr3.BigInt, _a1 error) *MockCCIPReader_LinkPriceUSD_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockCCIPReader_LinkPriceUSD_Call) RunAndReturn(run func(context.Context) (ccipocr3.BigInt, error)) *MockCCIPReader_LinkPriceUSD_Call {
	_c.Call.Return(run)
	return _c
}

// MsgsBetweenSeqNums provides a mock function with given fields: ctx, chain, seqNumRange
func (_m *MockCCIPReader) MsgsBetweenSeqNums(ctx context.Context, chain ccipocr3.ChainSelector, seqNumRange ccipocr3.SeqNumRange) ([]ccipocr3.Message, error) {
	ret := _m.Called(ctx, chain, seqNumRange)

	if len(ret) == 0 {
		panic("no return value specified for MsgsBetweenSeqNums")
	}

	var r0 []ccipocr3.Message
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ccipocr3.ChainSelector, ccipocr3.SeqNumRange) ([]ccipocr3.Message, error)); ok {
		return rf(ctx, chain, seqNumRange)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ccipocr3.ChainSelector, ccipocr3.SeqNumRange) []ccipocr3.Message); ok {
		r0 = rf(ctx, chain, seqNumRange)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]ccipocr3.Message)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ccipocr3.ChainSelector, ccipocr3.SeqNumRange) error); ok {
		r1 = rf(ctx, chain, seqNumRange)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCCIPReader_MsgsBetweenSeqNums_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MsgsBetweenSeqNums'
type MockCCIPReader_MsgsBetweenSeqNums_Call struct {
	*mock.Call
}

// MsgsBetweenSeqNums is a helper method to define mock.On call
//   - ctx context.Context
//   - chain ccipocr3.ChainSelector
//   - seqNumRange ccipocr3.SeqNumRange
func (_e *MockCCIPReader_Expecter) MsgsBetweenSeqNums(ctx interface{}, chain interface{}, seqNumRange interface{}) *MockCCIPReader_MsgsBetweenSeqNums_Call {
	return &MockCCIPReader_MsgsBetweenSeqNums_Call{Call: _e.mock.On("MsgsBetweenSeqNums", ctx, chain, seqNumRange)}
}

func (_c *MockCCIPReader_MsgsBetweenSeqNums_Call) Run(run func(ctx context.Context, chain ccipocr3.ChainSelector, seqNumRange ccipocr3.SeqNumRange)) *MockCCIPReader_MsgsBetweenSeqNums_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ccipocr3.ChainSelector), args[2].(ccipocr3.SeqNumRange))
	})
	return _c
}

func (_c *MockCCIPReader_MsgsBetweenSeqNums_Call) Return(_a0 []ccipocr3.Message, _a1 error) *MockCCIPReader_MsgsBetweenSeqNums_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockCCIPReader_MsgsBetweenSeqNums_Call) RunAndReturn(run func(context.Context, ccipocr3.ChainSelector, ccipocr3.SeqNumRange) ([]ccipocr3.Message, error)) *MockCCIPReader_MsgsBetweenSeqNums_Call {
	_c.Call.Return(run)
	return _c
}

// NextSeqNum provides a mock function with given fields: ctx, chains
func (_m *MockCCIPReader) NextSeqNum(ctx context.Context, chains []ccipocr3.ChainSelector) (map[ccipocr3.ChainSelector]ccipocr3.SeqNum, error) {
	ret := _m.Called(ctx, chains)

	if len(ret) == 0 {
		panic("no return value specified for NextSeqNum")
	}

	var r0 map[ccipocr3.ChainSelector]ccipocr3.SeqNum
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []ccipocr3.ChainSelector) (map[ccipocr3.ChainSelector]ccipocr3.SeqNum, error)); ok {
		return rf(ctx, chains)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []ccipocr3.ChainSelector) map[ccipocr3.ChainSelector]ccipocr3.SeqNum); ok {
		r0 = rf(ctx, chains)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[ccipocr3.ChainSelector]ccipocr3.SeqNum)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []ccipocr3.ChainSelector) error); ok {
		r1 = rf(ctx, chains)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCCIPReader_NextSeqNum_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NextSeqNum'
type MockCCIPReader_NextSeqNum_Call struct {
	*mock.Call
}

// NextSeqNum is a helper method to define mock.On call
//   - ctx context.Context
//   - chains []ccipocr3.ChainSelector
func (_e *MockCCIPReader_Expecter) NextSeqNum(ctx interface{}, chains interface{}) *MockCCIPReader_NextSeqNum_Call {
	return &MockCCIPReader_NextSeqNum_Call{Call: _e.mock.On("NextSeqNum", ctx, chains)}
}

func (_c *MockCCIPReader_NextSeqNum_Call) Run(run func(ctx context.Context, chains []ccipocr3.ChainSelector)) *MockCCIPReader_NextSeqNum_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]ccipocr3.ChainSelector))
	})
	return _c
}

func (_c *MockCCIPReader_NextSeqNum_Call) Return(seqNum map[ccipocr3.ChainSelector]ccipocr3.SeqNum, err error) *MockCCIPReader_NextSeqNum_Call {
	_c.Call.Return(seqNum, err)
	return _c
}

func (_c *MockCCIPReader_NextSeqNum_Call) RunAndReturn(run func(context.Context, []ccipocr3.ChainSelector) (map[ccipocr3.ChainSelector]ccipocr3.SeqNum, error)) *MockCCIPReader_NextSeqNum_Call {
	_c.Call.Return(run)
	return _c
}

// Nonces provides a mock function with given fields: ctx, addressesByChain
func (_m *MockCCIPReader) Nonces(ctx context.Context, addressesByChain map[ccipocr3.ChainSelector][]string) (map[ccipocr3.ChainSelector]map[string]uint64, error) {
	ret := _m.Called(ctx, addressesByChain)

	if len(ret) == 0 {
		panic("no return value specified for Nonces")
	}

	var r0 map[ccipocr3.ChainSelector]map[string]uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, map[ccipocr3.ChainSelector][]string) (map[ccipocr3.ChainSelector]map[string]uint64, error)); ok {
		return rf(ctx, addressesByChain)
	}
	if rf, ok := ret.Get(0).(func(context.Context, map[ccipocr3.ChainSelector][]string) map[ccipocr3.ChainSelector]map[string]uint64); ok {
		r0 = rf(ctx, addressesByChain)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[ccipocr3.ChainSelector]map[string]uint64)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, map[ccipocr3.ChainSelector][]string) error); ok {
		r1 = rf(ctx, addressesByChain)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCCIPReader_Nonces_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Nonces'
type MockCCIPReader_Nonces_Call struct {
	*mock.Call
}

// Nonces is a helper method to define mock.On call
//   - ctx context.Context
//   - addressesByChain map[ccipocr3.ChainSelector][]string
func (_e *MockCCIPReader_Expecter) Nonces(ctx interface{}, addressesByChain interface{}) *MockCCIPReader_Nonces_Call {
	return &MockCCIPReader_Nonces_Call{Call: _e.mock.On("Nonces", ctx, addressesByChain)}
}

func (_c *MockCCIPReader_Nonces_Call) Run(run func(ctx context.Context, addressesByChain map[ccipocr3.ChainSelector][]string)) *MockCCIPReader_Nonces_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(map[ccipocr3.ChainSelector][]string))
	})
	return _c
}

func (_c *MockCCIPReader_Nonces_Call) Return(_a0 map[ccipocr3.ChainSelector]map[string]uint64, _a1 error) *MockCCIPReader_Nonces_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockCCIPReader_Nonces_Call) RunAndReturn(run func(context.Context, map[ccipocr3.ChainSelector][]string) (map[ccipocr3.ChainSelector]map[string]uint64, error)) *MockCCIPReader_Nonces_Call {
	_c.Call.Return(run)
	return _c
}

// Sync provides a mock function with given fields: ctx, contracts
func (_m *MockCCIPReader) Sync(ctx context.Context, contracts reader.ContractAddresses) error {
	ret := _m.Called(ctx, contracts)

	if len(ret) == 0 {
		panic("no return value specified for Sync")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, reader.ContractAddresses) error); ok {
		r0 = rf(ctx, contracts)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockCCIPReader_Sync_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Sync'
type MockCCIPReader_Sync_Call struct {
	*mock.Call
}

// Sync is a helper method to define mock.On call
//   - ctx context.Context
//   - contracts reader.ContractAddresses
func (_e *MockCCIPReader_Expecter) Sync(ctx interface{}, contracts interface{}) *MockCCIPReader_Sync_Call {
	return &MockCCIPReader_Sync_Call{Call: _e.mock.On("Sync", ctx, contracts)}
}

func (_c *MockCCIPReader_Sync_Call) Run(run func(ctx context.Context, contracts reader.ContractAddresses)) *MockCCIPReader_Sync_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(reader.ContractAddresses))
	})
	return _c
}

func (_c *MockCCIPReader_Sync_Call) Return(_a0 error) *MockCCIPReader_Sync_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockCCIPReader_Sync_Call) RunAndReturn(run func(context.Context, reader.ContractAddresses) error) *MockCCIPReader_Sync_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockCCIPReader creates a new instance of MockCCIPReader. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockCCIPReader(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockCCIPReader {
	mock := &MockCCIPReader{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
