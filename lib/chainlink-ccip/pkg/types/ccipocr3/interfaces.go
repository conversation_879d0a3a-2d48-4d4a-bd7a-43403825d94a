package ccipocr3

import (
	ccipocr3common "github.com/smartcontractkit/chainlink-common/pkg/types/ccipocr3"
)

// Deprecated: Use ccipocr3common.UnknownAddress instead.
type CommitPluginCodec = ccipocr3common.CommitPluginCodec

// Deprecated: Use ccipocr3common.ExecutePluginCodec instead.
type ExecutePluginCodec = ccipocr3common.ExecutePluginCodec

// Deprecated: Use ccipocr3common.MessageHasher instead.
type MessageHasher = ccipocr3common.MessageHasher

// Deprecated: Use ccipocr3common.AddressCodec instead.
type AddressCodec = ccipocr3common.AddressCodec

// Deprecated: Use ccipocr3common.RMNCrypto instead.
type RMNCrypto = ccipocr3common.RMNCrypto

// Deprecated: Use ccipocr3common.TokenDataEncoder instead.
type TokenDataEncoder = ccipocr3common.TokenDataEncoder

// Deprecated: Use ccipocr3common.EstimateProvider instead.
type EstimateProvider = ccipocr3common.EstimateProvider
