// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v5.28.0
// source: pkg/ocrtypecodec/v1/ocrtypes.proto

package ocrtypecodecpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CommitQuery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MerkleRootQuery *MerkleRootQuery `protobuf:"bytes,1,opt,name=merkle_root_query,json=merkleRootQuery,proto3" json:"merkle_root_query,omitempty"`
}

func (x *CommitQuery) Reset() {
	*x = CommitQuery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommitQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommitQuery) ProtoMessage() {}

func (x *CommitQuery) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommitQuery.ProtoReflect.Descriptor instead.
func (*CommitQuery) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{0}
}

func (x *CommitQuery) GetMerkleRootQuery() *MerkleRootQuery {
	if x != nil {
		return x.MerkleRootQuery
	}
	return nil
}

type CommitObservation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MerkleRootObs         *MerkleRootObservation `protobuf:"bytes,1,opt,name=merkle_root_obs,json=merkleRootObs,proto3" json:"merkle_root_obs,omitempty"`
	TokenPriceObs         *TokenPriceObservation `protobuf:"bytes,2,opt,name=token_price_obs,json=tokenPriceObs,proto3" json:"token_price_obs,omitempty"`
	ChainFeeObs           *ChainFeeObservation   `protobuf:"bytes,3,opt,name=chain_fee_obs,json=chainFeeObs,proto3" json:"chain_fee_obs,omitempty"`
	DiscoveryObs          *DiscoveryObservation  `protobuf:"bytes,4,opt,name=discovery_obs,json=discoveryObs,proto3" json:"discovery_obs,omitempty"`
	FChain                map[uint64]int32       `protobuf:"bytes,5,rep,name=f_chain,json=fChain,proto3" json:"f_chain,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // chainSelector to f
	OnchainPriceOcrSeqNum uint64                 `protobuf:"varint,6,opt,name=onchain_price_ocr_seq_num,json=onchainPriceOcrSeqNum,proto3" json:"onchain_price_ocr_seq_num,omitempty"`                                       // the ocr sequence number of the last report with prices seen onchain
}

func (x *CommitObservation) Reset() {
	*x = CommitObservation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommitObservation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommitObservation) ProtoMessage() {}

func (x *CommitObservation) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommitObservation.ProtoReflect.Descriptor instead.
func (*CommitObservation) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{1}
}

func (x *CommitObservation) GetMerkleRootObs() *MerkleRootObservation {
	if x != nil {
		return x.MerkleRootObs
	}
	return nil
}

func (x *CommitObservation) GetTokenPriceObs() *TokenPriceObservation {
	if x != nil {
		return x.TokenPriceObs
	}
	return nil
}

func (x *CommitObservation) GetChainFeeObs() *ChainFeeObservation {
	if x != nil {
		return x.ChainFeeObs
	}
	return nil
}

func (x *CommitObservation) GetDiscoveryObs() *DiscoveryObservation {
	if x != nil {
		return x.DiscoveryObs
	}
	return nil
}

func (x *CommitObservation) GetFChain() map[uint64]int32 {
	if x != nil {
		return x.FChain
	}
	return nil
}

func (x *CommitObservation) GetOnchainPriceOcrSeqNum() uint64 {
	if x != nil {
		return x.OnchainPriceOcrSeqNum
	}
	return 0
}

type CommitOutcome struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MerkleRootOutcome *MerkleRootOutcome `protobuf:"bytes,1,opt,name=merkle_root_outcome,json=merkleRootOutcome,proto3" json:"merkle_root_outcome,omitempty"`
	TokenPriceOutcome *TokenPriceOutcome `protobuf:"bytes,2,opt,name=token_price_outcome,json=tokenPriceOutcome,proto3" json:"token_price_outcome,omitempty"`
	ChainFeeOutcome   *ChainFeeOutcome   `protobuf:"bytes,3,opt,name=chain_fee_outcome,json=chainFeeOutcome,proto3" json:"chain_fee_outcome,omitempty"`
	MainOutcome       *MainOutcome       `protobuf:"bytes,4,opt,name=main_outcome,json=mainOutcome,proto3" json:"main_outcome,omitempty"`
}

func (x *CommitOutcome) Reset() {
	*x = CommitOutcome{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommitOutcome) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommitOutcome) ProtoMessage() {}

func (x *CommitOutcome) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommitOutcome.ProtoReflect.Descriptor instead.
func (*CommitOutcome) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{2}
}

func (x *CommitOutcome) GetMerkleRootOutcome() *MerkleRootOutcome {
	if x != nil {
		return x.MerkleRootOutcome
	}
	return nil
}

func (x *CommitOutcome) GetTokenPriceOutcome() *TokenPriceOutcome {
	if x != nil {
		return x.TokenPriceOutcome
	}
	return nil
}

func (x *CommitOutcome) GetChainFeeOutcome() *ChainFeeOutcome {
	if x != nil {
		return x.ChainFeeOutcome
	}
	return nil
}

func (x *CommitOutcome) GetMainOutcome() *MainOutcome {
	if x != nil {
		return x.MainOutcome
	}
	return nil
}

type ExecObservation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommitReports         map[uint64]*CommitObservations `protobuf:"bytes,1,rep,name=commit_reports,json=commitReports,proto3" json:"commit_reports,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`     // chainSelector to commitObservations
	SeqNumsToMsgs         map[uint64]*SeqNumToMessage    `protobuf:"bytes,2,rep,name=seq_nums_to_msgs,json=seqNumsToMsgs,proto3" json:"seq_nums_to_msgs,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // chainSelector to seqNum to msg
	MsgHashes             map[uint64]*SeqNumToBytes      `protobuf:"bytes,3,rep,name=msg_hashes,json=msgHashes,proto3" json:"msg_hashes,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`                 // chainSelector to seqNum to bytes32
	TokenDataObservations *TokenDataObservations         `protobuf:"bytes,4,opt,name=token_data_observations,json=tokenDataObservations,proto3" json:"token_data_observations,omitempty"`
	// Deprecated: Marked as deprecated in pkg/ocrtypecodec/v1/ocrtypes.proto.
	CostlyMessages [][]byte                      `protobuf:"bytes,5,rep,name=costly_messages,json=costlyMessages,proto3" json:"costly_messages,omitempty"` // DEPRECATED: Message IDs of costly messages
	Nonces         map[uint64]*StringAddrToNonce `protobuf:"bytes,6,rep,name=nonces,proto3" json:"nonces,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Contracts      *DiscoveryObservation         `protobuf:"bytes,7,opt,name=contracts,proto3" json:"contracts,omitempty"`
	FChain         map[uint64]int32              `protobuf:"bytes,8,rep,name=f_chain,json=fChain,proto3" json:"f_chain,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // chainSelector to f
}

func (x *ExecObservation) Reset() {
	*x = ExecObservation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecObservation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecObservation) ProtoMessage() {}

func (x *ExecObservation) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecObservation.ProtoReflect.Descriptor instead.
func (*ExecObservation) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{3}
}

func (x *ExecObservation) GetCommitReports() map[uint64]*CommitObservations {
	if x != nil {
		return x.CommitReports
	}
	return nil
}

func (x *ExecObservation) GetSeqNumsToMsgs() map[uint64]*SeqNumToMessage {
	if x != nil {
		return x.SeqNumsToMsgs
	}
	return nil
}

func (x *ExecObservation) GetMsgHashes() map[uint64]*SeqNumToBytes {
	if x != nil {
		return x.MsgHashes
	}
	return nil
}

func (x *ExecObservation) GetTokenDataObservations() *TokenDataObservations {
	if x != nil {
		return x.TokenDataObservations
	}
	return nil
}

// Deprecated: Marked as deprecated in pkg/ocrtypecodec/v1/ocrtypes.proto.
func (x *ExecObservation) GetCostlyMessages() [][]byte {
	if x != nil {
		return x.CostlyMessages
	}
	return nil
}

func (x *ExecObservation) GetNonces() map[uint64]*StringAddrToNonce {
	if x != nil {
		return x.Nonces
	}
	return nil
}

func (x *ExecObservation) GetContracts() *DiscoveryObservation {
	if x != nil {
		return x.Contracts
	}
	return nil
}

func (x *ExecObservation) GetFChain() map[uint64]int32 {
	if x != nil {
		return x.FChain
	}
	return nil
}

type ExecOutcome struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PluginState          string                 `protobuf:"bytes,1,opt,name=plugin_state,json=pluginState,proto3" json:"plugin_state,omitempty"`
	CommitReports        []*CommitData          `protobuf:"bytes,2,rep,name=commit_reports,json=commitReports,proto3" json:"commit_reports,omitempty"`
	ExecutePluginReport  *ExecutePluginReport   `protobuf:"bytes,3,opt,name=execute_plugin_report,json=executePluginReport,proto3" json:"execute_plugin_report,omitempty"` // DEPRECATED: Use execute_plugin_reports instead
	ExecutePluginReports []*ExecutePluginReport `protobuf:"bytes,4,rep,name=execute_plugin_reports,json=executePluginReports,proto3" json:"execute_plugin_reports,omitempty"`
}

func (x *ExecOutcome) Reset() {
	*x = ExecOutcome{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecOutcome) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecOutcome) ProtoMessage() {}

func (x *ExecOutcome) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecOutcome.ProtoReflect.Descriptor instead.
func (*ExecOutcome) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{4}
}

func (x *ExecOutcome) GetPluginState() string {
	if x != nil {
		return x.PluginState
	}
	return ""
}

func (x *ExecOutcome) GetCommitReports() []*CommitData {
	if x != nil {
		return x.CommitReports
	}
	return nil
}

func (x *ExecOutcome) GetExecutePluginReport() *ExecutePluginReport {
	if x != nil {
		return x.ExecutePluginReport
	}
	return nil
}

func (x *ExecOutcome) GetExecutePluginReports() []*ExecutePluginReport {
	if x != nil {
		return x.ExecutePluginReports
	}
	return nil
}

type MerkleRootQuery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RetryRmnSignatures bool              `protobuf:"varint,1,opt,name=retry_rmn_signatures,json=retryRmnSignatures,proto3" json:"retry_rmn_signatures,omitempty"`
	RmnSignatures      *ReportSignatures `protobuf:"bytes,2,opt,name=rmn_signatures,json=rmnSignatures,proto3" json:"rmn_signatures,omitempty"`
}

func (x *MerkleRootQuery) Reset() {
	*x = MerkleRootQuery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MerkleRootQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerkleRootQuery) ProtoMessage() {}

func (x *MerkleRootQuery) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerkleRootQuery.ProtoReflect.Descriptor instead.
func (*MerkleRootQuery) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{5}
}

func (x *MerkleRootQuery) GetRetryRmnSignatures() bool {
	if x != nil {
		return x.RetryRmnSignatures
	}
	return false
}

func (x *MerkleRootQuery) GetRmnSignatures() *ReportSignatures {
	if x != nil {
		return x.RmnSignatures
	}
	return nil
}

type ReportSignatures struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Signatures  []*SignatureEcdsa  `protobuf:"bytes,1,rep,name=signatures,proto3" json:"signatures,omitempty"`
	LaneUpdates []*DestChainUpdate `protobuf:"bytes,2,rep,name=lane_updates,json=laneUpdates,proto3" json:"lane_updates,omitempty"`
}

func (x *ReportSignatures) Reset() {
	*x = ReportSignatures{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportSignatures) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportSignatures) ProtoMessage() {}

func (x *ReportSignatures) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportSignatures.ProtoReflect.Descriptor instead.
func (*ReportSignatures) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{6}
}

func (x *ReportSignatures) GetSignatures() []*SignatureEcdsa {
	if x != nil {
		return x.Signatures
	}
	return nil
}

func (x *ReportSignatures) GetLaneUpdates() []*DestChainUpdate {
	if x != nil {
		return x.LaneUpdates
	}
	return nil
}

type SignatureEcdsa struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	R []byte `protobuf:"bytes,1,opt,name=r,proto3" json:"r,omitempty"`
	S []byte `protobuf:"bytes,2,opt,name=s,proto3" json:"s,omitempty"`
}

func (x *SignatureEcdsa) Reset() {
	*x = SignatureEcdsa{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignatureEcdsa) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignatureEcdsa) ProtoMessage() {}

func (x *SignatureEcdsa) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignatureEcdsa.ProtoReflect.Descriptor instead.
func (*SignatureEcdsa) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{7}
}

func (x *SignatureEcdsa) GetR() []byte {
	if x != nil {
		return x.R
	}
	return nil
}

func (x *SignatureEcdsa) GetS() []byte {
	if x != nil {
		return x.S
	}
	return nil
}

type DestChainUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LaneSource  *SourceChainMeta `protobuf:"bytes,1,opt,name=lane_source,json=laneSource,proto3" json:"lane_source,omitempty"`
	SeqNumRange *SeqNumRange     `protobuf:"bytes,2,opt,name=seq_num_range,json=seqNumRange,proto3" json:"seq_num_range,omitempty"`
	Root        []byte           `protobuf:"bytes,3,opt,name=root,proto3" json:"root,omitempty"` // bytes32
}

func (x *DestChainUpdate) Reset() {
	*x = DestChainUpdate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DestChainUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DestChainUpdate) ProtoMessage() {}

func (x *DestChainUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DestChainUpdate.ProtoReflect.Descriptor instead.
func (*DestChainUpdate) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{8}
}

func (x *DestChainUpdate) GetLaneSource() *SourceChainMeta {
	if x != nil {
		return x.LaneSource
	}
	return nil
}

func (x *DestChainUpdate) GetSeqNumRange() *SeqNumRange {
	if x != nil {
		return x.SeqNumRange
	}
	return nil
}

func (x *DestChainUpdate) GetRoot() []byte {
	if x != nil {
		return x.Root
	}
	return nil
}

type MerkleRootObservation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MerkleRoots        []*MerkleRootChain `protobuf:"bytes,1,rep,name=merkle_roots,json=merkleRoots,proto3" json:"merkle_roots,omitempty"`
	RmnEnabledChains   map[uint64]bool    `protobuf:"bytes,2,rep,name=rmn_enabled_chains,json=rmnEnabledChains,proto3" json:"rmn_enabled_chains,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // chainSelector to bool
	OnRampMaxSeqNums   []*SeqNumChain     `protobuf:"bytes,3,rep,name=on_ramp_max_seq_nums,json=onRampMaxSeqNums,proto3" json:"on_ramp_max_seq_nums,omitempty"`
	OffRampNextSeqNums []*SeqNumChain     `protobuf:"bytes,4,rep,name=off_ramp_next_seq_nums,json=offRampNextSeqNums,proto3" json:"off_ramp_next_seq_nums,omitempty"`
	RmnRemoteConfig    *RmnRemoteConfig   `protobuf:"bytes,5,opt,name=rmn_remote_config,json=rmnRemoteConfig,proto3" json:"rmn_remote_config,omitempty"`
	FChain             map[uint64]int32   `protobuf:"bytes,6,rep,name=f_chain,json=fChain,proto3" json:"f_chain,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // chainSelector to f
}

func (x *MerkleRootObservation) Reset() {
	*x = MerkleRootObservation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MerkleRootObservation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerkleRootObservation) ProtoMessage() {}

func (x *MerkleRootObservation) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerkleRootObservation.ProtoReflect.Descriptor instead.
func (*MerkleRootObservation) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{9}
}

func (x *MerkleRootObservation) GetMerkleRoots() []*MerkleRootChain {
	if x != nil {
		return x.MerkleRoots
	}
	return nil
}

func (x *MerkleRootObservation) GetRmnEnabledChains() map[uint64]bool {
	if x != nil {
		return x.RmnEnabledChains
	}
	return nil
}

func (x *MerkleRootObservation) GetOnRampMaxSeqNums() []*SeqNumChain {
	if x != nil {
		return x.OnRampMaxSeqNums
	}
	return nil
}

func (x *MerkleRootObservation) GetOffRampNextSeqNums() []*SeqNumChain {
	if x != nil {
		return x.OffRampNextSeqNums
	}
	return nil
}

func (x *MerkleRootObservation) GetRmnRemoteConfig() *RmnRemoteConfig {
	if x != nil {
		return x.RmnRemoteConfig
	}
	return nil
}

func (x *MerkleRootObservation) GetFChain() map[uint64]int32 {
	if x != nil {
		return x.FChain
	}
	return nil
}

type RmnRemoteConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContractAddress  []byte              `protobuf:"bytes,1,opt,name=contract_address,json=contractAddress,proto3" json:"contract_address,omitempty"`
	ConfigDigest     []byte              `protobuf:"bytes,2,opt,name=config_digest,json=configDigest,proto3" json:"config_digest,omitempty"`
	Signers          []*RemoteSignerInfo `protobuf:"bytes,3,rep,name=signers,proto3" json:"signers,omitempty"`
	FSign            uint64              `protobuf:"varint,4,opt,name=f_sign,json=fSign,proto3" json:"f_sign,omitempty"`
	ConfigVersion    uint32              `protobuf:"varint,5,opt,name=config_version,json=configVersion,proto3" json:"config_version,omitempty"`
	RmnReportVersion []byte              `protobuf:"bytes,6,opt,name=rmn_report_version,json=rmnReportVersion,proto3" json:"rmn_report_version,omitempty"`
}

func (x *RmnRemoteConfig) Reset() {
	*x = RmnRemoteConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RmnRemoteConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RmnRemoteConfig) ProtoMessage() {}

func (x *RmnRemoteConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RmnRemoteConfig.ProtoReflect.Descriptor instead.
func (*RmnRemoteConfig) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{10}
}

func (x *RmnRemoteConfig) GetContractAddress() []byte {
	if x != nil {
		return x.ContractAddress
	}
	return nil
}

func (x *RmnRemoteConfig) GetConfigDigest() []byte {
	if x != nil {
		return x.ConfigDigest
	}
	return nil
}

func (x *RmnRemoteConfig) GetSigners() []*RemoteSignerInfo {
	if x != nil {
		return x.Signers
	}
	return nil
}

func (x *RmnRemoteConfig) GetFSign() uint64 {
	if x != nil {
		return x.FSign
	}
	return 0
}

func (x *RmnRemoteConfig) GetConfigVersion() uint32 {
	if x != nil {
		return x.ConfigVersion
	}
	return 0
}

func (x *RmnRemoteConfig) GetRmnReportVersion() []byte {
	if x != nil {
		return x.RmnReportVersion
	}
	return nil
}

type RemoteSignerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OnchainPublicKey []byte `protobuf:"bytes,1,opt,name=onchain_public_key,json=onchainPublicKey,proto3" json:"onchain_public_key,omitempty"`
	NodeIndex        uint64 `protobuf:"varint,2,opt,name=node_index,json=nodeIndex,proto3" json:"node_index,omitempty"`
}

func (x *RemoteSignerInfo) Reset() {
	*x = RemoteSignerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoteSignerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoteSignerInfo) ProtoMessage() {}

func (x *RemoteSignerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoteSignerInfo.ProtoReflect.Descriptor instead.
func (*RemoteSignerInfo) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{11}
}

func (x *RemoteSignerInfo) GetOnchainPublicKey() []byte {
	if x != nil {
		return x.OnchainPublicKey
	}
	return nil
}

func (x *RemoteSignerInfo) GetNodeIndex() uint64 {
	if x != nil {
		return x.NodeIndex
	}
	return 0
}

type TokenPriceObservation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FeedTokenPrices       map[string][]byte          `protobuf:"bytes,1,rep,name=feed_token_prices,json=feedTokenPrices,proto3" json:"feed_token_prices,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	FeeQuoterTokenUpdates map[string]*TimestampedBig `protobuf:"bytes,2,rep,name=fee_quoter_token_updates,json=feeQuoterTokenUpdates,proto3" json:"fee_quoter_token_updates,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	FChain                map[uint64]int32           `protobuf:"bytes,3,rep,name=f_chain,json=fChain,proto3" json:"f_chain,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // chainSelector to f
	Timestamp             *timestamppb.Timestamp     `protobuf:"bytes,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *TokenPriceObservation) Reset() {
	*x = TokenPriceObservation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TokenPriceObservation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenPriceObservation) ProtoMessage() {}

func (x *TokenPriceObservation) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenPriceObservation.ProtoReflect.Descriptor instead.
func (*TokenPriceObservation) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{12}
}

func (x *TokenPriceObservation) GetFeedTokenPrices() map[string][]byte {
	if x != nil {
		return x.FeedTokenPrices
	}
	return nil
}

func (x *TokenPriceObservation) GetFeeQuoterTokenUpdates() map[string]*TimestampedBig {
	if x != nil {
		return x.FeeQuoterTokenUpdates
	}
	return nil
}

func (x *TokenPriceObservation) GetFChain() map[uint64]int32 {
	if x != nil {
		return x.FChain
	}
	return nil
}

func (x *TokenPriceObservation) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

type ChainFeeObservation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FeeComponents     map[uint64]*ChainFeeComponents `protobuf:"bytes,1,rep,name=fee_components,json=feeComponents,proto3" json:"fee_components,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`               // chainSelector to ChainFeeComponents
	NativeTokenPrices map[uint64][]byte              `protobuf:"bytes,2,rep,name=native_token_prices,json=nativeTokenPrices,proto3" json:"native_token_prices,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // chainSelector to bigInt bytes
	ChainFeeUpdates   map[uint64]*ChainFeeUpdate     `protobuf:"bytes,3,rep,name=chain_fee_updates,json=chainFeeUpdates,proto3" json:"chain_fee_updates,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`       // chainSelector to ChainFeeUpdate
	FChain            map[uint64]int32               `protobuf:"bytes,4,rep,name=f_chain,json=fChain,proto3" json:"f_chain,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`                                   // chainSelector to f
	TimestampNow      *timestamppb.Timestamp         `protobuf:"bytes,5,opt,name=timestamp_now,json=timestampNow,proto3" json:"timestamp_now,omitempty"`
}

func (x *ChainFeeObservation) Reset() {
	*x = ChainFeeObservation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChainFeeObservation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChainFeeObservation) ProtoMessage() {}

func (x *ChainFeeObservation) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChainFeeObservation.ProtoReflect.Descriptor instead.
func (*ChainFeeObservation) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{13}
}

func (x *ChainFeeObservation) GetFeeComponents() map[uint64]*ChainFeeComponents {
	if x != nil {
		return x.FeeComponents
	}
	return nil
}

func (x *ChainFeeObservation) GetNativeTokenPrices() map[uint64][]byte {
	if x != nil {
		return x.NativeTokenPrices
	}
	return nil
}

func (x *ChainFeeObservation) GetChainFeeUpdates() map[uint64]*ChainFeeUpdate {
	if x != nil {
		return x.ChainFeeUpdates
	}
	return nil
}

func (x *ChainFeeObservation) GetFChain() map[uint64]int32 {
	if x != nil {
		return x.FChain
	}
	return nil
}

func (x *ChainFeeObservation) GetTimestampNow() *timestamppb.Timestamp {
	if x != nil {
		return x.TimestampNow
	}
	return nil
}

type ChainFeeComponents struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExecutionFee        []byte `protobuf:"bytes,1,opt,name=execution_fee,json=executionFee,proto3" json:"execution_fee,omitempty"`                        // bigInt bytes
	DataAvailabilityFee []byte `protobuf:"bytes,2,opt,name=data_availability_fee,json=dataAvailabilityFee,proto3" json:"data_availability_fee,omitempty"` // bigInt bytes
}

func (x *ChainFeeComponents) Reset() {
	*x = ChainFeeComponents{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChainFeeComponents) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChainFeeComponents) ProtoMessage() {}

func (x *ChainFeeComponents) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChainFeeComponents.ProtoReflect.Descriptor instead.
func (*ChainFeeComponents) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{14}
}

func (x *ChainFeeComponents) GetExecutionFee() []byte {
	if x != nil {
		return x.ExecutionFee
	}
	return nil
}

func (x *ChainFeeComponents) GetDataAvailabilityFee() []byte {
	if x != nil {
		return x.DataAvailabilityFee
	}
	return nil
}

type ChainFeeUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChainFee  *ComponentsUSDPrices   `protobuf:"bytes,1,opt,name=chain_fee,json=chainFee,proto3" json:"chain_fee,omitempty"`
	Timestamp *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *ChainFeeUpdate) Reset() {
	*x = ChainFeeUpdate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChainFeeUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChainFeeUpdate) ProtoMessage() {}

func (x *ChainFeeUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChainFeeUpdate.ProtoReflect.Descriptor instead.
func (*ChainFeeUpdate) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{15}
}

func (x *ChainFeeUpdate) GetChainFee() *ComponentsUSDPrices {
	if x != nil {
		return x.ChainFee
	}
	return nil
}

func (x *ChainFeeUpdate) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

type ComponentsUSDPrices struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExecutionFeePriceUsd []byte `protobuf:"bytes,1,opt,name=execution_fee_price_usd,json=executionFeePriceUsd,proto3" json:"execution_fee_price_usd,omitempty"` // bigInt bytes
	DataAvFeePriceUsd    []byte `protobuf:"bytes,2,opt,name=data_av_fee_price_usd,json=dataAvFeePriceUsd,proto3" json:"data_av_fee_price_usd,omitempty"`        // bigInt bytes
}

func (x *ComponentsUSDPrices) Reset() {
	*x = ComponentsUSDPrices{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ComponentsUSDPrices) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComponentsUSDPrices) ProtoMessage() {}

func (x *ComponentsUSDPrices) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComponentsUSDPrices.ProtoReflect.Descriptor instead.
func (*ComponentsUSDPrices) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{16}
}

func (x *ComponentsUSDPrices) GetExecutionFeePriceUsd() []byte {
	if x != nil {
		return x.ExecutionFeePriceUsd
	}
	return nil
}

func (x *ComponentsUSDPrices) GetDataAvFeePriceUsd() []byte {
	if x != nil {
		return x.DataAvFeePriceUsd
	}
	return nil
}

type DiscoveryObservation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FChain        map[uint64]int32            `protobuf:"bytes,1,rep,name=f_chain,json=fChain,proto3" json:"f_chain,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // chainSelector to f
	ContractNames *ContractNameChainAddresses `protobuf:"bytes,2,opt,name=contract_names,json=contractNames,proto3" json:"contract_names,omitempty"`
}

func (x *DiscoveryObservation) Reset() {
	*x = DiscoveryObservation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DiscoveryObservation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiscoveryObservation) ProtoMessage() {}

func (x *DiscoveryObservation) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiscoveryObservation.ProtoReflect.Descriptor instead.
func (*DiscoveryObservation) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{17}
}

func (x *DiscoveryObservation) GetFChain() map[uint64]int32 {
	if x != nil {
		return x.FChain
	}
	return nil
}

func (x *DiscoveryObservation) GetContractNames() *ContractNameChainAddresses {
	if x != nil {
		return x.ContractNames
	}
	return nil
}

type ContractNameChainAddresses struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Addresses map[string]*ChainAddressMap `protobuf:"bytes,1,rep,name=addresses,proto3" json:"addresses,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // contract name to chain to address
}

func (x *ContractNameChainAddresses) Reset() {
	*x = ContractNameChainAddresses{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContractNameChainAddresses) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContractNameChainAddresses) ProtoMessage() {}

func (x *ContractNameChainAddresses) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContractNameChainAddresses.ProtoReflect.Descriptor instead.
func (*ContractNameChainAddresses) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{18}
}

func (x *ContractNameChainAddresses) GetAddresses() map[string]*ChainAddressMap {
	if x != nil {
		return x.Addresses
	}
	return nil
}

type ChainAddressMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChainAddresses map[uint64][]byte `protobuf:"bytes,1,rep,name=chain_addresses,json=chainAddresses,proto3" json:"chain_addresses,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // chainSelector to address
}

func (x *ChainAddressMap) Reset() {
	*x = ChainAddressMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChainAddressMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChainAddressMap) ProtoMessage() {}

func (x *ChainAddressMap) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChainAddressMap.ProtoReflect.Descriptor instead.
func (*ChainAddressMap) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{19}
}

func (x *ChainAddressMap) GetChainAddresses() map[uint64][]byte {
	if x != nil {
		return x.ChainAddresses
	}
	return nil
}

type MerkleRootOutcome struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OutcomeType                     int32              `protobuf:"varint,1,opt,name=outcome_type,json=outcomeType,proto3" json:"outcome_type,omitempty"`
	RangesSelectedForReport         []*ChainRange      `protobuf:"bytes,2,rep,name=ranges_selected_for_report,json=rangesSelectedForReport,proto3" json:"ranges_selected_for_report,omitempty"`
	RootsToReport                   []*MerkleRootChain `protobuf:"bytes,3,rep,name=roots_to_report,json=rootsToReport,proto3" json:"roots_to_report,omitempty"`
	RmnEnabledChains                map[uint64]bool    `protobuf:"bytes,4,rep,name=rmn_enabled_chains,json=rmnEnabledChains,proto3" json:"rmn_enabled_chains,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // chainSelector to bool
	OffRampNextSeqNums              []*SeqNumChain     `protobuf:"bytes,5,rep,name=off_ramp_next_seq_nums,json=offRampNextSeqNums,proto3" json:"off_ramp_next_seq_nums,omitempty"`
	ReportTransmissionCheckAttempts uint32             `protobuf:"varint,6,opt,name=report_transmission_check_attempts,json=reportTransmissionCheckAttempts,proto3" json:"report_transmission_check_attempts,omitempty"`
	RmnReportSignatures             []*SignatureEcdsa  `protobuf:"bytes,7,rep,name=rmn_report_signatures,json=rmnReportSignatures,proto3" json:"rmn_report_signatures,omitempty"`
	RmnRemoteCfg                    *RmnRemoteConfig   `protobuf:"bytes,8,opt,name=rmn_remote_cfg,json=rmnRemoteCfg,proto3" json:"rmn_remote_cfg,omitempty"`
}

func (x *MerkleRootOutcome) Reset() {
	*x = MerkleRootOutcome{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MerkleRootOutcome) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerkleRootOutcome) ProtoMessage() {}

func (x *MerkleRootOutcome) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerkleRootOutcome.ProtoReflect.Descriptor instead.
func (*MerkleRootOutcome) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{20}
}

func (x *MerkleRootOutcome) GetOutcomeType() int32 {
	if x != nil {
		return x.OutcomeType
	}
	return 0
}

func (x *MerkleRootOutcome) GetRangesSelectedForReport() []*ChainRange {
	if x != nil {
		return x.RangesSelectedForReport
	}
	return nil
}

func (x *MerkleRootOutcome) GetRootsToReport() []*MerkleRootChain {
	if x != nil {
		return x.RootsToReport
	}
	return nil
}

func (x *MerkleRootOutcome) GetRmnEnabledChains() map[uint64]bool {
	if x != nil {
		return x.RmnEnabledChains
	}
	return nil
}

func (x *MerkleRootOutcome) GetOffRampNextSeqNums() []*SeqNumChain {
	if x != nil {
		return x.OffRampNextSeqNums
	}
	return nil
}

func (x *MerkleRootOutcome) GetReportTransmissionCheckAttempts() uint32 {
	if x != nil {
		return x.ReportTransmissionCheckAttempts
	}
	return 0
}

func (x *MerkleRootOutcome) GetRmnReportSignatures() []*SignatureEcdsa {
	if x != nil {
		return x.RmnReportSignatures
	}
	return nil
}

func (x *MerkleRootOutcome) GetRmnRemoteCfg() *RmnRemoteConfig {
	if x != nil {
		return x.RmnRemoteCfg
	}
	return nil
}

type TokenPriceOutcome struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TokenPrices map[string][]byte `protobuf:"bytes,1,rep,name=token_prices,json=tokenPrices,proto3" json:"token_prices,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *TokenPriceOutcome) Reset() {
	*x = TokenPriceOutcome{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TokenPriceOutcome) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenPriceOutcome) ProtoMessage() {}

func (x *TokenPriceOutcome) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenPriceOutcome.ProtoReflect.Descriptor instead.
func (*TokenPriceOutcome) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{21}
}

func (x *TokenPriceOutcome) GetTokenPrices() map[string][]byte {
	if x != nil {
		return x.TokenPrices
	}
	return nil
}

type ChainFeeOutcome struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GasPrices []*GasPriceChain `protobuf:"bytes,1,rep,name=gas_prices,json=gasPrices,proto3" json:"gas_prices,omitempty"`
}

func (x *ChainFeeOutcome) Reset() {
	*x = ChainFeeOutcome{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChainFeeOutcome) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChainFeeOutcome) ProtoMessage() {}

func (x *ChainFeeOutcome) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChainFeeOutcome.ProtoReflect.Descriptor instead.
func (*ChainFeeOutcome) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{22}
}

func (x *ChainFeeOutcome) GetGasPrices() []*GasPriceChain {
	if x != nil {
		return x.GasPrices
	}
	return nil
}

type GasPriceChain struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChainSel uint64 `protobuf:"varint,1,opt,name=chain_sel,json=chainSel,proto3" json:"chain_sel,omitempty"`
	GasPrice []byte `protobuf:"bytes,2,opt,name=gas_price,json=gasPrice,proto3" json:"gas_price,omitempty"`
}

func (x *GasPriceChain) Reset() {
	*x = GasPriceChain{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GasPriceChain) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GasPriceChain) ProtoMessage() {}

func (x *GasPriceChain) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GasPriceChain.ProtoReflect.Descriptor instead.
func (*GasPriceChain) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{23}
}

func (x *GasPriceChain) GetChainSel() uint64 {
	if x != nil {
		return x.ChainSel
	}
	return 0
}

func (x *GasPriceChain) GetGasPrice() []byte {
	if x != nil {
		return x.GasPrice
	}
	return nil
}

type MainOutcome struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InflightPriceOcrSequenceNumber uint64 `protobuf:"varint,1,opt,name=inflight_price_ocr_sequence_number,json=inflightPriceOcrSequenceNumber,proto3" json:"inflight_price_ocr_sequence_number,omitempty"`
	RemainingPriceChecks           int32  `protobuf:"varint,2,opt,name=remaining_price_checks,json=remainingPriceChecks,proto3" json:"remaining_price_checks,omitempty"`
}

func (x *MainOutcome) Reset() {
	*x = MainOutcome{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MainOutcome) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MainOutcome) ProtoMessage() {}

func (x *MainOutcome) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MainOutcome.ProtoReflect.Descriptor instead.
func (*MainOutcome) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{24}
}

func (x *MainOutcome) GetInflightPriceOcrSequenceNumber() uint64 {
	if x != nil {
		return x.InflightPriceOcrSequenceNumber
	}
	return 0
}

func (x *MainOutcome) GetRemainingPriceChecks() int32 {
	if x != nil {
		return x.RemainingPriceChecks
	}
	return 0
}

type CommitObservations struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommitData []*CommitData `protobuf:"bytes,1,rep,name=commit_data,json=commitData,proto3" json:"commit_data,omitempty"`
}

func (x *CommitObservations) Reset() {
	*x = CommitObservations{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommitObservations) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommitObservations) ProtoMessage() {}

func (x *CommitObservations) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommitObservations.ProtoReflect.Descriptor instead.
func (*CommitObservations) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{25}
}

func (x *CommitObservations) GetCommitData() []*CommitData {
	if x != nil {
		return x.CommitData
	}
	return nil
}

type CommitData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceChain         uint64                 `protobuf:"varint,1,opt,name=source_chain,json=sourceChain,proto3" json:"source_chain,omitempty"`
	OnRampAddress       []byte                 `protobuf:"bytes,2,opt,name=on_ramp_address,json=onRampAddress,proto3" json:"on_ramp_address,omitempty"`
	Timestamp           *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	BlockNum            uint64                 `protobuf:"varint,4,opt,name=block_num,json=blockNum,proto3" json:"block_num,omitempty"`
	MerkleRoot          []byte                 `protobuf:"bytes,5,opt,name=merkle_root,json=merkleRoot,proto3" json:"merkle_root,omitempty"` // Should be 32bytes
	SequenceNumberRange *SeqNumRange           `protobuf:"bytes,6,opt,name=sequence_number_range,json=sequenceNumberRange,proto3" json:"sequence_number_range,omitempty"`
	ExecutedMessages    []uint64               `protobuf:"varint,7,rep,packed,name=executed_messages,json=executedMessages,proto3" json:"executed_messages,omitempty"`
	Messages            []*Message             `protobuf:"bytes,8,rep,name=messages,proto3" json:"messages,omitempty"`
	Hashes              [][]byte               `protobuf:"bytes,9,rep,name=hashes,proto3" json:"hashes,omitempty"` // Each bytes should be 32bytes
	// Deprecated: Marked as deprecated in pkg/ocrtypecodec/v1/ocrtypes.proto.
	CostlyMessages   [][]byte            `protobuf:"bytes,10,rep,name=costly_messages,json=costlyMessages,proto3" json:"costly_messages,omitempty"` // DEPRECATED: Message IDs of costly messages
	MessageTokenData []*MessageTokenData `protobuf:"bytes,11,rep,name=message_token_data,json=messageTokenData,proto3" json:"message_token_data,omitempty"`
}

func (x *CommitData) Reset() {
	*x = CommitData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommitData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommitData) ProtoMessage() {}

func (x *CommitData) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommitData.ProtoReflect.Descriptor instead.
func (*CommitData) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{26}
}

func (x *CommitData) GetSourceChain() uint64 {
	if x != nil {
		return x.SourceChain
	}
	return 0
}

func (x *CommitData) GetOnRampAddress() []byte {
	if x != nil {
		return x.OnRampAddress
	}
	return nil
}

func (x *CommitData) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *CommitData) GetBlockNum() uint64 {
	if x != nil {
		return x.BlockNum
	}
	return 0
}

func (x *CommitData) GetMerkleRoot() []byte {
	if x != nil {
		return x.MerkleRoot
	}
	return nil
}

func (x *CommitData) GetSequenceNumberRange() *SeqNumRange {
	if x != nil {
		return x.SequenceNumberRange
	}
	return nil
}

func (x *CommitData) GetExecutedMessages() []uint64 {
	if x != nil {
		return x.ExecutedMessages
	}
	return nil
}

func (x *CommitData) GetMessages() []*Message {
	if x != nil {
		return x.Messages
	}
	return nil
}

func (x *CommitData) GetHashes() [][]byte {
	if x != nil {
		return x.Hashes
	}
	return nil
}

// Deprecated: Marked as deprecated in pkg/ocrtypecodec/v1/ocrtypes.proto.
func (x *CommitData) GetCostlyMessages() [][]byte {
	if x != nil {
		return x.CostlyMessages
	}
	return nil
}

func (x *CommitData) GetMessageTokenData() []*MessageTokenData {
	if x != nil {
		return x.MessageTokenData
	}
	return nil
}

type MessageTokenData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TokenData []*TokenData `protobuf:"bytes,1,rep,name=token_data,json=tokenData,proto3" json:"token_data,omitempty"`
}

func (x *MessageTokenData) Reset() {
	*x = MessageTokenData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageTokenData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageTokenData) ProtoMessage() {}

func (x *MessageTokenData) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageTokenData.ProtoReflect.Descriptor instead.
func (*MessageTokenData) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{27}
}

func (x *MessageTokenData) GetTokenData() []*TokenData {
	if x != nil {
		return x.TokenData
	}
	return nil
}

type TokenData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ready bool   `protobuf:"varint,1,opt,name=ready,proto3" json:"ready,omitempty"`
	Data  []byte `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *TokenData) Reset() {
	*x = TokenData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TokenData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenData) ProtoMessage() {}

func (x *TokenData) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenData.ProtoReflect.Descriptor instead.
func (*TokenData) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{28}
}

func (x *TokenData) GetReady() bool {
	if x != nil {
		return x.Ready
	}
	return false
}

func (x *TokenData) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type SeqNumToMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Messages map[uint64]*Message `protobuf:"bytes,1,rep,name=messages,proto3" json:"messages,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *SeqNumToMessage) Reset() {
	*x = SeqNumToMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeqNumToMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeqNumToMessage) ProtoMessage() {}

func (x *SeqNumToMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeqNumToMessage.ProtoReflect.Descriptor instead.
func (*SeqNumToMessage) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{29}
}

func (x *SeqNumToMessage) GetMessages() map[uint64]*Message {
	if x != nil {
		return x.Messages
	}
	return nil
}

type SeqNumToBytes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeqNumToBytes map[uint64][]byte `protobuf:"bytes,1,rep,name=seq_num_to_bytes,json=seqNumToBytes,proto3" json:"seq_num_to_bytes,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *SeqNumToBytes) Reset() {
	*x = SeqNumToBytes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeqNumToBytes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeqNumToBytes) ProtoMessage() {}

func (x *SeqNumToBytes) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeqNumToBytes.ProtoReflect.Descriptor instead.
func (*SeqNumToBytes) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{30}
}

func (x *SeqNumToBytes) GetSeqNumToBytes() map[uint64][]byte {
	if x != nil {
		return x.SeqNumToBytes
	}
	return nil
}

type TokenDataObservations struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TokenData map[uint64]*SeqNumToTokenData `protobuf:"bytes,1,rep,name=token_data,json=tokenData,proto3" json:"token_data,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *TokenDataObservations) Reset() {
	*x = TokenDataObservations{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TokenDataObservations) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenDataObservations) ProtoMessage() {}

func (x *TokenDataObservations) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenDataObservations.ProtoReflect.Descriptor instead.
func (*TokenDataObservations) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{31}
}

func (x *TokenDataObservations) GetTokenData() map[uint64]*SeqNumToTokenData {
	if x != nil {
		return x.TokenData
	}
	return nil
}

type SeqNumToTokenData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TokenData map[uint64]*MessageTokenData `protobuf:"bytes,1,rep,name=token_data,json=tokenData,proto3" json:"token_data,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *SeqNumToTokenData) Reset() {
	*x = SeqNumToTokenData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeqNumToTokenData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeqNumToTokenData) ProtoMessage() {}

func (x *SeqNumToTokenData) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeqNumToTokenData.ProtoReflect.Descriptor instead.
func (*SeqNumToTokenData) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{32}
}

func (x *SeqNumToTokenData) GetTokenData() map[uint64]*MessageTokenData {
	if x != nil {
		return x.TokenData
	}
	return nil
}

type Message struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header         *RampMessageHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Sender         []byte             `protobuf:"bytes,2,opt,name=sender,proto3" json:"sender,omitempty"` // address
	Data           []byte             `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Receiver       []byte             `protobuf:"bytes,4,opt,name=receiver,proto3" json:"receiver,omitempty"` // address
	ExtraArgs      []byte             `protobuf:"bytes,5,opt,name=extra_args,json=extraArgs,proto3" json:"extra_args,omitempty"`
	FeeToken       []byte             `protobuf:"bytes,7,opt,name=fee_token,json=feeToken,proto3" json:"fee_token,omitempty"`                     // address
	FeeTokenAmount []byte             `protobuf:"bytes,8,opt,name=fee_token_amount,json=feeTokenAmount,proto3" json:"fee_token_amount,omitempty"` // bigInt bytes
	FeeValueJuels  []byte             `protobuf:"bytes,9,opt,name=fee_value_juels,json=feeValueJuels,proto3" json:"fee_value_juels,omitempty"`    // bigInt bytes
	TokenAmounts   []*RampTokenAmount `protobuf:"bytes,10,rep,name=token_amounts,json=tokenAmounts,proto3" json:"token_amounts,omitempty"`
}

func (x *Message) Reset() {
	*x = Message{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Message) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message) ProtoMessage() {}

func (x *Message) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message.ProtoReflect.Descriptor instead.
func (*Message) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{33}
}

func (x *Message) GetHeader() *RampMessageHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *Message) GetSender() []byte {
	if x != nil {
		return x.Sender
	}
	return nil
}

func (x *Message) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Message) GetReceiver() []byte {
	if x != nil {
		return x.Receiver
	}
	return nil
}

func (x *Message) GetExtraArgs() []byte {
	if x != nil {
		return x.ExtraArgs
	}
	return nil
}

func (x *Message) GetFeeToken() []byte {
	if x != nil {
		return x.FeeToken
	}
	return nil
}

func (x *Message) GetFeeTokenAmount() []byte {
	if x != nil {
		return x.FeeTokenAmount
	}
	return nil
}

func (x *Message) GetFeeValueJuels() []byte {
	if x != nil {
		return x.FeeValueJuels
	}
	return nil
}

func (x *Message) GetTokenAmounts() []*RampTokenAmount {
	if x != nil {
		return x.TokenAmounts
	}
	return nil
}

type RampMessageHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MessageId           []byte `protobuf:"bytes,1,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	SourceChainSelector uint64 `protobuf:"varint,2,opt,name=source_chain_selector,json=sourceChainSelector,proto3" json:"source_chain_selector,omitempty"`
	DestChainSelector   uint64 `protobuf:"varint,3,opt,name=dest_chain_selector,json=destChainSelector,proto3" json:"dest_chain_selector,omitempty"`
	SequenceNumber      uint64 `protobuf:"varint,4,opt,name=sequence_number,json=sequenceNumber,proto3" json:"sequence_number,omitempty"`
	Nonce               uint64 `protobuf:"varint,5,opt,name=nonce,proto3" json:"nonce,omitempty"`
	MsgHash             []byte `protobuf:"bytes,6,opt,name=msg_hash,json=msgHash,proto3" json:"msg_hash,omitempty"`
	OnRamp              []byte `protobuf:"bytes,7,opt,name=on_ramp,json=onRamp,proto3" json:"on_ramp,omitempty"` // address
	TxHash              string `protobuf:"bytes,8,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`
}

func (x *RampMessageHeader) Reset() {
	*x = RampMessageHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RampMessageHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RampMessageHeader) ProtoMessage() {}

func (x *RampMessageHeader) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RampMessageHeader.ProtoReflect.Descriptor instead.
func (*RampMessageHeader) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{34}
}

func (x *RampMessageHeader) GetMessageId() []byte {
	if x != nil {
		return x.MessageId
	}
	return nil
}

func (x *RampMessageHeader) GetSourceChainSelector() uint64 {
	if x != nil {
		return x.SourceChainSelector
	}
	return 0
}

func (x *RampMessageHeader) GetDestChainSelector() uint64 {
	if x != nil {
		return x.DestChainSelector
	}
	return 0
}

func (x *RampMessageHeader) GetSequenceNumber() uint64 {
	if x != nil {
		return x.SequenceNumber
	}
	return 0
}

func (x *RampMessageHeader) GetNonce() uint64 {
	if x != nil {
		return x.Nonce
	}
	return 0
}

func (x *RampMessageHeader) GetMsgHash() []byte {
	if x != nil {
		return x.MsgHash
	}
	return nil
}

func (x *RampMessageHeader) GetOnRamp() []byte {
	if x != nil {
		return x.OnRamp
	}
	return nil
}

func (x *RampMessageHeader) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

type RampTokenAmount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourcePoolAddress []byte `protobuf:"bytes,1,opt,name=source_pool_address,json=sourcePoolAddress,proto3" json:"source_pool_address,omitempty"` // address
	DestTokenAddress  []byte `protobuf:"bytes,2,opt,name=dest_token_address,json=destTokenAddress,proto3" json:"dest_token_address,omitempty"`    // address
	ExtraData         []byte `protobuf:"bytes,3,opt,name=extra_data,json=extraData,proto3" json:"extra_data,omitempty"`
	Amount            []byte `protobuf:"bytes,4,opt,name=amount,proto3" json:"amount,omitempty"` // bigInt bytes
	DestExecData      []byte `protobuf:"bytes,5,opt,name=dest_exec_data,json=destExecData,proto3" json:"dest_exec_data,omitempty"`
}

func (x *RampTokenAmount) Reset() {
	*x = RampTokenAmount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RampTokenAmount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RampTokenAmount) ProtoMessage() {}

func (x *RampTokenAmount) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RampTokenAmount.ProtoReflect.Descriptor instead.
func (*RampTokenAmount) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{35}
}

func (x *RampTokenAmount) GetSourcePoolAddress() []byte {
	if x != nil {
		return x.SourcePoolAddress
	}
	return nil
}

func (x *RampTokenAmount) GetDestTokenAddress() []byte {
	if x != nil {
		return x.DestTokenAddress
	}
	return nil
}

func (x *RampTokenAmount) GetExtraData() []byte {
	if x != nil {
		return x.ExtraData
	}
	return nil
}

func (x *RampTokenAmount) GetAmount() []byte {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *RampTokenAmount) GetDestExecData() []byte {
	if x != nil {
		return x.DestExecData
	}
	return nil
}

type StringAddrToNonce struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nonces map[string]uint64 `protobuf:"bytes,1,rep,name=nonces,proto3" json:"nonces,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // address string to nonce
}

func (x *StringAddrToNonce) Reset() {
	*x = StringAddrToNonce{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringAddrToNonce) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringAddrToNonce) ProtoMessage() {}

func (x *StringAddrToNonce) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringAddrToNonce.ProtoReflect.Descriptor instead.
func (*StringAddrToNonce) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{36}
}

func (x *StringAddrToNonce) GetNonces() map[string]uint64 {
	if x != nil {
		return x.Nonces
	}
	return nil
}

type ExecutePluginReport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChainReports []*ChainReport `protobuf:"bytes,1,rep,name=chain_reports,json=chainReports,proto3" json:"chain_reports,omitempty"`
}

func (x *ExecutePluginReport) Reset() {
	*x = ExecutePluginReport{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecutePluginReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecutePluginReport) ProtoMessage() {}

func (x *ExecutePluginReport) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecutePluginReport.ProtoReflect.Descriptor instead.
func (*ExecutePluginReport) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{37}
}

func (x *ExecutePluginReport) GetChainReports() []*ChainReport {
	if x != nil {
		return x.ChainReports
	}
	return nil
}

type ChainReport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceChainSelector uint64           `protobuf:"varint,1,opt,name=source_chain_selector,json=sourceChainSelector,proto3" json:"source_chain_selector,omitempty"`
	Messages            []*Message       `protobuf:"bytes,2,rep,name=messages,proto3" json:"messages,omitempty"`
	OffchainTokenData   []*RepeatedBytes `protobuf:"bytes,3,rep,name=offchain_token_data,json=offchainTokenData,proto3" json:"offchain_token_data,omitempty"`
	Proofs              [][]byte         `protobuf:"bytes,4,rep,name=proofs,proto3" json:"proofs,omitempty"`
	ProofFlagBits       []byte           `protobuf:"bytes,5,opt,name=proof_flag_bits,json=proofFlagBits,proto3" json:"proof_flag_bits,omitempty"`
}

func (x *ChainReport) Reset() {
	*x = ChainReport{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChainReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChainReport) ProtoMessage() {}

func (x *ChainReport) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChainReport.ProtoReflect.Descriptor instead.
func (*ChainReport) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{38}
}

func (x *ChainReport) GetSourceChainSelector() uint64 {
	if x != nil {
		return x.SourceChainSelector
	}
	return 0
}

func (x *ChainReport) GetMessages() []*Message {
	if x != nil {
		return x.Messages
	}
	return nil
}

func (x *ChainReport) GetOffchainTokenData() []*RepeatedBytes {
	if x != nil {
		return x.OffchainTokenData
	}
	return nil
}

func (x *ChainReport) GetProofs() [][]byte {
	if x != nil {
		return x.Proofs
	}
	return nil
}

func (x *ChainReport) GetProofFlagBits() []byte {
	if x != nil {
		return x.ProofFlagBits
	}
	return nil
}

type RepeatedBytes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items [][]byte `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *RepeatedBytes) Reset() {
	*x = RepeatedBytes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedBytes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedBytes) ProtoMessage() {}

func (x *RepeatedBytes) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedBytes.ProtoReflect.Descriptor instead.
func (*RepeatedBytes) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{39}
}

func (x *RepeatedBytes) GetItems() [][]byte {
	if x != nil {
		return x.Items
	}
	return nil
}

type SeqNumRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MinMsgNr uint64 `protobuf:"varint,1,opt,name=min_msg_nr,json=minMsgNr,proto3" json:"min_msg_nr,omitempty"`
	MaxMsgNr uint64 `protobuf:"varint,2,opt,name=max_msg_nr,json=maxMsgNr,proto3" json:"max_msg_nr,omitempty"`
}

func (x *SeqNumRange) Reset() {
	*x = SeqNumRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeqNumRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeqNumRange) ProtoMessage() {}

func (x *SeqNumRange) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeqNumRange.ProtoReflect.Descriptor instead.
func (*SeqNumRange) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{40}
}

func (x *SeqNumRange) GetMinMsgNr() uint64 {
	if x != nil {
		return x.MinMsgNr
	}
	return 0
}

func (x *SeqNumRange) GetMaxMsgNr() uint64 {
	if x != nil {
		return x.MaxMsgNr
	}
	return 0
}

type SeqNumChain struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChainSel uint64 `protobuf:"varint,1,opt,name=chain_sel,json=chainSel,proto3" json:"chain_sel,omitempty"`
	SeqNum   uint64 `protobuf:"varint,2,opt,name=seq_num,json=seqNum,proto3" json:"seq_num,omitempty"`
}

func (x *SeqNumChain) Reset() {
	*x = SeqNumChain{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeqNumChain) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeqNumChain) ProtoMessage() {}

func (x *SeqNumChain) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeqNumChain.ProtoReflect.Descriptor instead.
func (*SeqNumChain) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{41}
}

func (x *SeqNumChain) GetChainSel() uint64 {
	if x != nil {
		return x.ChainSel
	}
	return 0
}

func (x *SeqNumChain) GetSeqNum() uint64 {
	if x != nil {
		return x.SeqNum
	}
	return 0
}

type ChainRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChainSel    uint64       `protobuf:"varint,1,opt,name=chain_sel,json=chainSel,proto3" json:"chain_sel,omitempty"`
	SeqNumRange *SeqNumRange `protobuf:"bytes,2,opt,name=seq_num_range,json=seqNumRange,proto3" json:"seq_num_range,omitempty"`
}

func (x *ChainRange) Reset() {
	*x = ChainRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChainRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChainRange) ProtoMessage() {}

func (x *ChainRange) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChainRange.ProtoReflect.Descriptor instead.
func (*ChainRange) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{42}
}

func (x *ChainRange) GetChainSel() uint64 {
	if x != nil {
		return x.ChainSel
	}
	return 0
}

func (x *ChainRange) GetSeqNumRange() *SeqNumRange {
	if x != nil {
		return x.SeqNumRange
	}
	return nil
}

type SourceChainMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceChainSelector uint64 `protobuf:"varint,1,opt,name=source_chain_selector,json=sourceChainSelector,proto3" json:"source_chain_selector,omitempty"`
	OnrampAddress       []byte `protobuf:"bytes,2,opt,name=onramp_address,json=onrampAddress,proto3" json:"onramp_address,omitempty"`
}

func (x *SourceChainMeta) Reset() {
	*x = SourceChainMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SourceChainMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SourceChainMeta) ProtoMessage() {}

func (x *SourceChainMeta) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SourceChainMeta.ProtoReflect.Descriptor instead.
func (*SourceChainMeta) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{43}
}

func (x *SourceChainMeta) GetSourceChainSelector() uint64 {
	if x != nil {
		return x.SourceChainSelector
	}
	return 0
}

func (x *SourceChainMeta) GetOnrampAddress() []byte {
	if x != nil {
		return x.OnrampAddress
	}
	return nil
}

type MerkleRootChain struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChainSel      uint64       `protobuf:"varint,1,opt,name=chain_sel,json=chainSel,proto3" json:"chain_sel,omitempty"`
	OnRampAddress []byte       `protobuf:"bytes,2,opt,name=on_ramp_address,json=onRampAddress,proto3" json:"on_ramp_address,omitempty"`
	SeqNumsRange  *SeqNumRange `protobuf:"bytes,3,opt,name=seq_nums_range,json=seqNumsRange,proto3" json:"seq_nums_range,omitempty"`
	MerkleRoot    []byte       `protobuf:"bytes,4,opt,name=merkle_root,json=merkleRoot,proto3" json:"merkle_root,omitempty"`
}

func (x *MerkleRootChain) Reset() {
	*x = MerkleRootChain{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MerkleRootChain) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerkleRootChain) ProtoMessage() {}

func (x *MerkleRootChain) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerkleRootChain.ProtoReflect.Descriptor instead.
func (*MerkleRootChain) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{44}
}

func (x *MerkleRootChain) GetChainSel() uint64 {
	if x != nil {
		return x.ChainSel
	}
	return 0
}

func (x *MerkleRootChain) GetOnRampAddress() []byte {
	if x != nil {
		return x.OnRampAddress
	}
	return nil
}

func (x *MerkleRootChain) GetSeqNumsRange() *SeqNumRange {
	if x != nil {
		return x.SeqNumsRange
	}
	return nil
}

func (x *MerkleRootChain) GetMerkleRoot() []byte {
	if x != nil {
		return x.MerkleRoot
	}
	return nil
}

type TimestampedBig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Value     []byte                 `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *TimestampedBig) Reset() {
	*x = TimestampedBig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimestampedBig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimestampedBig) ProtoMessage() {}

func (x *TimestampedBig) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimestampedBig.ProtoReflect.Descriptor instead.
func (*TimestampedBig) Descriptor() ([]byte, []int) {
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP(), []int{45}
}

func (x *TimestampedBig) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *TimestampedBig) GetValue() []byte {
	if x != nil {
		return x.Value
	}
	return nil
}

var File_pkg_ocrtypecodec_v1_ocrtypes_proto protoreflect.FileDescriptor

var file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDesc = []byte{
	0x0a, 0x22, 0x70, 0x6b, 0x67, 0x2f, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64,
	0x65, 0x63, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70,
	0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5f, 0x0a, 0x0b, 0x43, 0x6f,
	0x6d, 0x6d, 0x69, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x50, 0x0a, 0x11, 0x6d, 0x65, 0x72,
	0x6b, 0x6c, 0x65, 0x5f, 0x72, 0x6f, 0x6f, 0x74, 0x5f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79,
	0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x72, 0x6b, 0x6c,
	0x65, 0x52, 0x6f, 0x6f, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x0f, 0x6d, 0x65, 0x72, 0x6b,
	0x6c, 0x65, 0x52, 0x6f, 0x6f, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79, 0x22, 0x9b, 0x04, 0x0a, 0x11,
	0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x4f, 0x62, 0x73, 0x65, 0x72, 0x76, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x52, 0x0a, 0x0f, 0x6d, 0x65, 0x72, 0x6b, 0x6c, 0x65, 0x5f, 0x72, 0x6f, 0x6f, 0x74,
	0x5f, 0x6f, 0x62, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x70, 0x6b, 0x67,
	0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x4d, 0x65, 0x72, 0x6b, 0x6c, 0x65, 0x52, 0x6f, 0x6f, 0x74, 0x4f, 0x62, 0x73, 0x65, 0x72,
	0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x6d, 0x65, 0x72, 0x6b, 0x6c, 0x65, 0x52, 0x6f,
	0x6f, 0x74, 0x4f, 0x62, 0x73, 0x12, 0x52, 0x0a, 0x0f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x5f, 0x6f, 0x62, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65,
	0x63, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x4f,
	0x62, 0x73, 0x65, 0x72, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x4f, 0x62, 0x73, 0x12, 0x4c, 0x0a, 0x0d, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x6f, 0x62, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f,
	0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x46, 0x65, 0x65, 0x4f,
	0x62, 0x73, 0x65, 0x72, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x46, 0x65, 0x65, 0x4f, 0x62, 0x73, 0x12, 0x4e, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x79, 0x5f, 0x6f, 0x62, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65,
	0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x4f, 0x62,
	0x73, 0x65, 0x72, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x79, 0x4f, 0x62, 0x73, 0x12, 0x4b, 0x0a, 0x07, 0x66, 0x5f, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f,
	0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x4f, 0x62, 0x73, 0x65, 0x72, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x46, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x66, 0x43,
	0x68, 0x61, 0x69, 0x6e, 0x12, 0x38, 0x0a, 0x19, 0x6f, 0x6e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x6f, 0x63, 0x72, 0x5f, 0x73, 0x65, 0x71, 0x5f, 0x6e, 0x75,
	0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x15, 0x6f, 0x6e, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x4f, 0x63, 0x72, 0x53, 0x65, 0x71, 0x4e, 0x75, 0x6d, 0x1a, 0x39,
	0x0a, 0x0b, 0x46, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xd6, 0x02, 0x0a, 0x0d, 0x43, 0x6f,
	0x6d, 0x6d, 0x69, 0x74, 0x4f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x56, 0x0a, 0x13, 0x6d,
	0x65, 0x72, 0x6b, 0x6c, 0x65, 0x5f, 0x72, 0x6f, 0x6f, 0x74, 0x5f, 0x6f, 0x75, 0x74, 0x63, 0x6f,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f,
	0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4d,
	0x65, 0x72, 0x6b, 0x6c, 0x65, 0x52, 0x6f, 0x6f, 0x74, 0x4f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65,
	0x52, 0x11, 0x6d, 0x65, 0x72, 0x6b, 0x6c, 0x65, 0x52, 0x6f, 0x6f, 0x74, 0x4f, 0x75, 0x74, 0x63,
	0x6f, 0x6d, 0x65, 0x12, 0x56, 0x0a, 0x13, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x5f, 0x6f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f,
	0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x4f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65, 0x52, 0x11, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x4f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x50, 0x0a, 0x11, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x6f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72,
	0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61,
	0x69, 0x6e, 0x46, 0x65, 0x65, 0x4f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65, 0x52, 0x0f, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x46, 0x65, 0x65, 0x4f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x43, 0x0a,
	0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70,
	0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x4f, 0x75,
	0x74, 0x63, 0x6f, 0x6d, 0x65, 0x52, 0x0b, 0x6d, 0x61, 0x69, 0x6e, 0x4f, 0x75, 0x74, 0x63, 0x6f,
	0x6d, 0x65, 0x22, 0xe9, 0x08, 0x0a, 0x0f, 0x45, 0x78, 0x65, 0x63, 0x4f, 0x62, 0x73, 0x65, 0x72,
	0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5e, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74,
	0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37,
	0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65,
	0x63, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x4f, 0x62, 0x73, 0x65, 0x72, 0x76, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x60, 0x0a, 0x10, 0x73, 0x65, 0x71, 0x5f, 0x6e, 0x75,
	0x6d, 0x73, 0x5f, 0x74, 0x6f, 0x5f, 0x6d, 0x73, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x37, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f,
	0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x4f, 0x62, 0x73, 0x65, 0x72,
	0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x65, 0x71, 0x4e, 0x75, 0x6d, 0x73, 0x54, 0x6f,
	0x4d, 0x73, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x73, 0x65, 0x71, 0x4e, 0x75,
	0x6d, 0x73, 0x54, 0x6f, 0x4d, 0x73, 0x67, 0x73, 0x12, 0x52, 0x0a, 0x0a, 0x6d, 0x73, 0x67, 0x5f,
	0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x70,
	0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x4f, 0x62, 0x73, 0x65, 0x72, 0x76, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x4d, 0x73, 0x67, 0x48, 0x61, 0x73, 0x68, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x09, 0x6d, 0x73, 0x67, 0x48, 0x61, 0x73, 0x68, 0x65, 0x73, 0x12, 0x62, 0x0a, 0x17,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6f, 0x62, 0x73, 0x65, 0x72,
	0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x4f, 0x62, 0x73,
	0x65, 0x72, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x15, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x44, 0x61, 0x74, 0x61, 0x4f, 0x62, 0x73, 0x65, 0x72, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x2b, 0x0a, 0x0f, 0x63, 0x6f, 0x73, 0x74, 0x6c, 0x79, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0c, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0e, 0x63,
	0x6f, 0x73, 0x74, 0x6c, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x12, 0x48, 0x0a,
	0x06, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e,
	0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x4f, 0x62, 0x73, 0x65, 0x72, 0x76, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x4e, 0x6f, 0x6e, 0x63, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x06, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x47, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x70, 0x6b, 0x67,
	0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x4f, 0x62, 0x73, 0x65, 0x72, 0x76,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73,
	0x12, 0x49, 0x0a, 0x07, 0x66, 0x5f, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x08, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x30, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63,
	0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x4f, 0x62, 0x73, 0x65,
	0x72, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x46, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x06, 0x66, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x1a, 0x69, 0x0a, 0x12, 0x43,
	0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x3d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65,
	0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x4f,
	0x62, 0x73, 0x65, 0x72, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x66, 0x0a, 0x12, 0x53, 0x65, 0x71, 0x4e, 0x75, 0x6d,
	0x73, 0x54, 0x6f, 0x4d, 0x73, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3a,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x71, 0x4e, 0x75, 0x6d, 0x54, 0x6f, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x60,
	0x0a, 0x0e, 0x4d, 0x73, 0x67, 0x48, 0x61, 0x73, 0x68, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x38, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63,
	0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x71, 0x4e, 0x75, 0x6d, 0x54, 0x6f,
	0x42, 0x79, 0x74, 0x65, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x61, 0x0a, 0x0b, 0x4e, 0x6f, 0x6e, 0x63, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x3c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f,
	0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64,
	0x72, 0x54, 0x6f, 0x4e, 0x6f, 0x6e, 0x63, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x39, 0x0a, 0x0b, 0x46, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb6,
	0x02, 0x0a, 0x0b, 0x45, 0x78, 0x65, 0x63, 0x4f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x46, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x6b, 0x67, 0x2e,
	0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x6d,
	0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x5c, 0x0a, 0x15, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x65, 0x5f, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f,
	0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x13, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x50, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x5e, 0x0a, 0x16, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x5f, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63,
	0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x52, 0x14, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x22, 0x91, 0x01, 0x0a, 0x0f, 0x4d, 0x65, 0x72, 0x6b,
	0x6c, 0x65, 0x52, 0x6f, 0x6f, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x30, 0x0a, 0x14, 0x72,
	0x65, 0x74, 0x72, 0x79, 0x5f, 0x72, 0x6d, 0x6e, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x72, 0x65, 0x74, 0x72, 0x79,
	0x52, 0x6d, 0x6e, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x4c, 0x0a,
	0x0e, 0x72, 0x6d, 0x6e, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74,
	0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x0d, 0x72, 0x6d,
	0x6e, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x22, 0xa0, 0x01, 0x0a, 0x10,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73,
	0x12, 0x43, 0x0a, 0x0a, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79,
	0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x45, 0x63, 0x64, 0x73, 0x61, 0x52, 0x0a, 0x73, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x47, 0x0a, 0x0c, 0x6c, 0x61, 0x6e, 0x65, 0x5f, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x70, 0x6b,
	0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x73, 0x74, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x0b, 0x6c, 0x61, 0x6e, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x22, 0x2c,
	0x0a, 0x0e, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x45, 0x63, 0x64, 0x73, 0x61,
	0x12, 0x0c, 0x0a, 0x01, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x01, 0x72, 0x12, 0x0c,
	0x0a, 0x01, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x01, 0x73, 0x22, 0xb2, 0x01, 0x0a,
	0x0f, 0x44, 0x65, 0x73, 0x74, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x12, 0x45, 0x0a, 0x0b, 0x6c, 0x61, 0x6e, 0x65, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74,
	0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0a, 0x6c, 0x61, 0x6e,
	0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x44, 0x0a, 0x0d, 0x73, 0x65, 0x71, 0x5f, 0x6e,
	0x75, 0x6d, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65,
	0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x71, 0x4e, 0x75, 0x6d, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x0b, 0x73, 0x65, 0x71, 0x4e, 0x75, 0x6d, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x72, 0x6f, 0x6f, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x72, 0x6f, 0x6f,
	0x74, 0x22, 0x9b, 0x05, 0x0a, 0x15, 0x4d, 0x65, 0x72, 0x6b, 0x6c, 0x65, 0x52, 0x6f, 0x6f, 0x74,
	0x4f, 0x62, 0x73, 0x65, 0x72, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x47, 0x0a, 0x0c, 0x6d,
	0x65, 0x72, 0x6b, 0x6c, 0x65, 0x5f, 0x72, 0x6f, 0x6f, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63,
	0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x72, 0x6b, 0x6c, 0x65, 0x52, 0x6f,
	0x6f, 0x74, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x0b, 0x6d, 0x65, 0x72, 0x6b, 0x6c, 0x65, 0x52,
	0x6f, 0x6f, 0x74, 0x73, 0x12, 0x6e, 0x0a, 0x12, 0x72, 0x6d, 0x6e, 0x5f, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x5f, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x40, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f,
	0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x72, 0x6b, 0x6c, 0x65, 0x52, 0x6f, 0x6f,
	0x74, 0x4f, 0x62, 0x73, 0x65, 0x72, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x52, 0x6d, 0x6e,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x10, 0x72, 0x6d, 0x6e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x43, 0x68,
	0x61, 0x69, 0x6e, 0x73, 0x12, 0x50, 0x0a, 0x14, 0x6f, 0x6e, 0x5f, 0x72, 0x61, 0x6d, 0x70, 0x5f,
	0x6d, 0x61, 0x78, 0x5f, 0x73, 0x65, 0x71, 0x5f, 0x6e, 0x75, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65,
	0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x71, 0x4e, 0x75, 0x6d, 0x43,
	0x68, 0x61, 0x69, 0x6e, 0x52, 0x10, 0x6f, 0x6e, 0x52, 0x61, 0x6d, 0x70, 0x4d, 0x61, 0x78, 0x53,
	0x65, 0x71, 0x4e, 0x75, 0x6d, 0x73, 0x12, 0x54, 0x0a, 0x16, 0x6f, 0x66, 0x66, 0x5f, 0x72, 0x61,
	0x6d, 0x70, 0x5f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x73, 0x65, 0x71, 0x5f, 0x6e, 0x75, 0x6d, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72,
	0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x71,
	0x4e, 0x75, 0x6d, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x12, 0x6f, 0x66, 0x66, 0x52, 0x61, 0x6d,
	0x70, 0x4e, 0x65, 0x78, 0x74, 0x53, 0x65, 0x71, 0x4e, 0x75, 0x6d, 0x73, 0x12, 0x50, 0x0a, 0x11,
	0x72, 0x6d, 0x6e, 0x5f, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63,
	0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6d,
	0x6e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0f, 0x72,
	0x6d, 0x6e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4f,
	0x0a, 0x07, 0x66, 0x5f, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x36, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64,
	0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x72, 0x6b, 0x6c, 0x65, 0x52, 0x6f, 0x6f, 0x74,
	0x4f, 0x62, 0x73, 0x65, 0x72, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x46, 0x43, 0x68, 0x61,
	0x69, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x66, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x1a,
	0x43, 0x0a, 0x15, 0x52, 0x6d, 0x6e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x43, 0x68, 0x61,
	0x69, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x1a, 0x39, 0x0a, 0x0b, 0x46, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x8e, 0x02, 0x0a, 0x0f, 0x52, 0x6d, 0x6e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0f, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x23,
	0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x64, 0x69, 0x67, 0x65, 0x73, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x69, 0x67,
	0x65, 0x73, 0x74, 0x12, 0x3f, 0x0a, 0x07, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79,
	0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x74,
	0x65, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x73, 0x69, 0x67,
	0x6e, 0x65, 0x72, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x66, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x66, 0x53, 0x69, 0x67, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x6d, 0x6e, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x10,
	0x72, 0x6d, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x22, 0x5f, 0x0a, 0x10, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2c, 0x0a, 0x12, 0x6f, 0x6e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f,
	0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x10, 0x6f, 0x6e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b,
	0x65, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x22, 0xfd, 0x04, 0x0a, 0x15, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x4f, 0x62, 0x73, 0x65, 0x72, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x6b, 0x0a, 0x11, 0x66,
	0x65, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72,
	0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x4f, 0x62, 0x73, 0x65, 0x72, 0x76, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x66, 0x65, 0x65, 0x64, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x73, 0x12, 0x7e, 0x0a, 0x18, 0x66, 0x65, 0x65, 0x5f,
	0x71, 0x75, 0x6f, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x70, 0x6b, 0x67,
	0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x4f, 0x62, 0x73, 0x65, 0x72,
	0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x46, 0x65, 0x65, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x72,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x15, 0x66, 0x65, 0x65, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x12, 0x4f, 0x0a, 0x07, 0x66, 0x5f, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x70, 0x6b, 0x67, 0x2e,
	0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x4f, 0x62, 0x73, 0x65, 0x72, 0x76,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x46, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x06, 0x66, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x38, 0x0a, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x1a, 0x42, 0x0a, 0x14, 0x46, 0x65, 0x65, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x6d, 0x0a, 0x1a, 0x46, 0x65, 0x65, 0x51, 0x75,
	0x6f, 0x74, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x39, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72,
	0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x65, 0x64, 0x42, 0x69, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x39, 0x0a, 0x0b, 0x46, 0x43, 0x68, 0x61, 0x69, 0x6e,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0xba, 0x06, 0x0a, 0x13, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x46, 0x65, 0x65, 0x4f, 0x62,
	0x73, 0x65, 0x72, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x62, 0x0a, 0x0e, 0x66, 0x65, 0x65,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x3b, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63,
	0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x46, 0x65, 0x65,
	0x4f, 0x62, 0x73, 0x65, 0x72, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x46, 0x65, 0x65, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d,
	0x66, 0x65, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x6f, 0x0a,
	0x13, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x70, 0x6b, 0x67,
	0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x46, 0x65, 0x65, 0x4f, 0x62, 0x73, 0x65, 0x72, 0x76, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x6e, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x73, 0x12, 0x69,
	0x0a, 0x11, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x70, 0x6b, 0x67, 0x2e,
	0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x68, 0x61, 0x69, 0x6e, 0x46, 0x65, 0x65, 0x4f, 0x62, 0x73, 0x65, 0x72, 0x76, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x46, 0x65, 0x65, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x46,
	0x65, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x12, 0x4d, 0x0a, 0x07, 0x66, 0x5f, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x70, 0x6b, 0x67,
	0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x46, 0x65, 0x65, 0x4f, 0x62, 0x73, 0x65, 0x72, 0x76, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x46, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x06, 0x66, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x3f, 0x0a, 0x0d, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6e, 0x6f, 0x77, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4e, 0x6f, 0x77, 0x1a, 0x69, 0x0a, 0x12, 0x46, 0x65, 0x65,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x3d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f,
	0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x46, 0x65, 0x65, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x1a, 0x44, 0x0a, 0x16, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x67, 0x0a, 0x14, 0x43, 0x68,
	0x61, 0x69, 0x6e, 0x46, 0x65, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x39, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70,
	0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x46,
	0x65, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x39, 0x0a, 0x0b, 0x46, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x6d,
	0x0a, 0x12, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x46, 0x65, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x65, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x66,
	0x65, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x13, 0x64, 0x61, 0x74, 0x61, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x46, 0x65, 0x65, 0x22, 0x91, 0x01,
	0x0a, 0x0e, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x46, 0x65, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x12, 0x45, 0x0a, 0x09, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70,
	0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x73, 0x55, 0x53, 0x44, 0x50, 0x72, 0x69, 0x63, 0x65, 0x73, 0x52, 0x08, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x46, 0x65, 0x65, 0x12, 0x38, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x22, 0x7e, 0x0a, 0x13, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x55,
	0x53, 0x44, 0x50, 0x72, 0x69, 0x63, 0x65, 0x73, 0x12, 0x35, 0x0a, 0x17, 0x65, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f,
	0x75, 0x73, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x14, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x46, 0x65, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x55, 0x73, 0x64, 0x12,
	0x30, 0x0a, 0x15, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x61, 0x76, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x73, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x11,
	0x64, 0x61, 0x74, 0x61, 0x41, 0x76, 0x46, 0x65, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x55, 0x73,
	0x64, 0x22, 0xf9, 0x01, 0x0a, 0x14, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x4f,
	0x62, 0x73, 0x65, 0x72, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a, 0x07, 0x66, 0x5f,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x70, 0x6b,
	0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x4f, 0x62, 0x73, 0x65, 0x72,
	0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x46, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x06, 0x66, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x56, 0x0a, 0x0e, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65,
	0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x65, 0x73, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x46, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xde, 0x01,
	0x0a, 0x1a, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x68,
	0x61, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x12, 0x5c, 0x0a, 0x09,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x3e, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64,
	0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73,
	0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x1a, 0x62, 0x0a, 0x0e, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3a,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x4d, 0x61, 0x70, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb7,
	0x01, 0x0a, 0x0f, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4d,
	0x61, 0x70, 0x12, 0x61, 0x0a, 0x0f, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x70, 0x6b,
	0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4d, 0x61,
	0x70, 0x2e, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x65, 0x73, 0x1a, 0x41, 0x0a, 0x13, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xdb, 0x05, 0x0a, 0x11, 0x4d, 0x65, 0x72,
	0x6b, 0x6c, 0x65, 0x52, 0x6f, 0x6f, 0x74, 0x4f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x6f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x5c, 0x0a, 0x1a, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74,
	0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x69,
	0x6e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x17, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x53, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12,
	0x4c, 0x0a, 0x0f, 0x72, 0x6f, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x6f, 0x5f, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f,
	0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4d,
	0x65, 0x72, 0x6b, 0x6c, 0x65, 0x52, 0x6f, 0x6f, 0x74, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x0d,
	0x72, 0x6f, 0x6f, 0x74, 0x73, 0x54, 0x6f, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x6a, 0x0a,
	0x12, 0x72, 0x6d, 0x6e, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x5f, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x70, 0x6b, 0x67, 0x2e,
	0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x4d, 0x65, 0x72, 0x6b, 0x6c, 0x65, 0x52, 0x6f, 0x6f, 0x74, 0x4f, 0x75, 0x74, 0x63, 0x6f, 0x6d,
	0x65, 0x2e, 0x52, 0x6d, 0x6e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x43, 0x68, 0x61, 0x69,
	0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x10, 0x72, 0x6d, 0x6e, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x73, 0x12, 0x54, 0x0a, 0x16, 0x6f, 0x66, 0x66,
	0x5f, 0x72, 0x61, 0x6d, 0x70, 0x5f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x73, 0x65, 0x71, 0x5f, 0x6e,
	0x75, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x6b, 0x67, 0x2e,
	0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x71, 0x4e, 0x75, 0x6d, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x12, 0x6f, 0x66, 0x66,
	0x52, 0x61, 0x6d, 0x70, 0x4e, 0x65, 0x78, 0x74, 0x53, 0x65, 0x71, 0x4e, 0x75, 0x6d, 0x73, 0x12,
	0x4b, 0x0a, 0x22, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x61, 0x74, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x1f, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x12, 0x57, 0x0a, 0x15,
	0x72, 0x6d, 0x6e, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x6b,
	0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x45, 0x63, 0x64, 0x73, 0x61,
	0x52, 0x13, 0x72, 0x6d, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x4a, 0x0a, 0x0e, 0x72, 0x6d, 0x6e, 0x5f, 0x72, 0x65, 0x6d,
	0x6f, 0x74, 0x65, 0x5f, 0x63, 0x66, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6d, 0x6e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x0c, 0x72, 0x6d, 0x6e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x43, 0x66,
	0x67, 0x1a, 0x43, 0x0a, 0x15, 0x52, 0x6d, 0x6e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x43,
	0x68, 0x61, 0x69, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xaf, 0x01, 0x0a, 0x11, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x4f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x5a, 0x0a, 0x0c,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x37, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65,
	0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x4f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x73, 0x1a, 0x3e, 0x0a, 0x10, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x54, 0x0a, 0x0f, 0x43, 0x68, 0x61, 0x69,
	0x6e, 0x46, 0x65, 0x65, 0x4f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x0a, 0x67,
	0x61, 0x73, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64,
	0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x61, 0x73, 0x50, 0x72, 0x69, 0x63, 0x65, 0x43, 0x68,
	0x61, 0x69, 0x6e, 0x52, 0x09, 0x67, 0x61, 0x73, 0x50, 0x72, 0x69, 0x63, 0x65, 0x73, 0x22, 0x49,
	0x0a, 0x0d, 0x47, 0x61, 0x73, 0x50, 0x72, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x12,
	0x1b, 0x0a, 0x09, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x73, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x08, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x6c, 0x12, 0x1b, 0x0a, 0x09,
	0x67, 0x61, 0x73, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x08, 0x67, 0x61, 0x73, 0x50, 0x72, 0x69, 0x63, 0x65, 0x22, 0x8f, 0x01, 0x0a, 0x0b, 0x4d, 0x61,
	0x69, 0x6e, 0x4f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x4a, 0x0a, 0x22, 0x69, 0x6e, 0x66,
	0x6c, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x6f, 0x63, 0x72, 0x5f,
	0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x1e, 0x69, 0x6e, 0x66, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x4f, 0x63, 0x72, 0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x34, 0x0a, 0x16, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69,
	0x6e, 0x67, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x22, 0x56, 0x0a, 0x12, 0x43,
	0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x4f, 0x62, 0x73, 0x65, 0x72, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x40, 0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72,
	0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d,
	0x6d, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x22, 0xa6, 0x04, 0x0a, 0x0a, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x43, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x6f, 0x6e, 0x5f, 0x72, 0x61, 0x6d, 0x70,
	0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d,
	0x6f, 0x6e, 0x52, 0x61, 0x6d, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x38, 0x0a,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x4e, 0x75, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x6b, 0x6c, 0x65, 0x5f, 0x72,
	0x6f, 0x6f, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x6b, 0x6c,
	0x65, 0x52, 0x6f, 0x6f, 0x74, 0x12, 0x54, 0x0a, 0x15, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79,
	0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x71, 0x4e, 0x75,
	0x6d, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x13, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x04, 0x52, 0x10, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x12, 0x38, 0x0a, 0x08, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70, 0x6b, 0x67,
	0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03,
	0x28, 0x0c, 0x52, 0x06, 0x68, 0x61, 0x73, 0x68, 0x65, 0x73, 0x12, 0x2b, 0x0a, 0x0f, 0x63, 0x6f,
	0x73, 0x74, 0x6c, 0x79, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x0a, 0x20,
	0x03, 0x28, 0x0c, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0e, 0x63, 0x6f, 0x73, 0x74, 0x6c, 0x79, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x12, 0x53, 0x0a, 0x12, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0b, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70,
	0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x10, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x22, 0x51, 0x0a, 0x10,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x3d, 0x0a, 0x0a, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79,
	0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x22,
	0x35, 0x0a, 0x09, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05,
	0x72, 0x65, 0x61, 0x64, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x72, 0x65, 0x61,
	0x64, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xbc, 0x01, 0x0a, 0x0f, 0x53, 0x65, 0x71, 0x4e, 0x75,
	0x6d, 0x54, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x4e, 0x0a, 0x08, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x70,
	0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x71, 0x4e, 0x75, 0x6d, 0x54, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x1a, 0x59, 0x0a, 0x0d, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x32, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70,
	0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb1, 0x01, 0x0a, 0x0d, 0x53, 0x65, 0x71, 0x4e, 0x75, 0x6d,
	0x54, 0x6f, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x5e, 0x0a, 0x10, 0x73, 0x65, 0x71, 0x5f, 0x6e,
	0x75, 0x6d, 0x5f, 0x74, 0x6f, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x35, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63,
	0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x71, 0x4e, 0x75, 0x6d, 0x54, 0x6f,
	0x42, 0x79, 0x74, 0x65, 0x73, 0x2e, 0x53, 0x65, 0x71, 0x4e, 0x75, 0x6d, 0x54, 0x6f, 0x42, 0x79,
	0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x73, 0x65, 0x71, 0x4e, 0x75, 0x6d,
	0x54, 0x6f, 0x42, 0x79, 0x74, 0x65, 0x73, 0x1a, 0x40, 0x0a, 0x12, 0x53, 0x65, 0x71, 0x4e, 0x75,
	0x6d, 0x54, 0x6f, 0x42, 0x79, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xd7, 0x01, 0x0a, 0x15, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x4f, 0x62, 0x73, 0x65, 0x72, 0x76, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x58, 0x0a, 0x0a, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63,
	0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x4f, 0x62, 0x73, 0x65, 0x72, 0x76, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x64, 0x0a,
	0x0e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x3c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f,
	0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x71, 0x4e, 0x75, 0x6d, 0x54, 0x6f, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0xce, 0x01, 0x0a, 0x11, 0x53, 0x65, 0x71, 0x4e, 0x75, 0x6d, 0x54, 0x6f,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x54, 0x0a, 0x0a, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e,
	0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x71, 0x4e, 0x75, 0x6d, 0x54, 0x6f, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x1a,
	0x63, 0x0a, 0x0e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x3b, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65,
	0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0xea, 0x02, 0x0a, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x3e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f,
	0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x61, 0x6d, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1a, 0x0a, 0x08,
	0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08,
	0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x5f, 0x61, 0x72, 0x67, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x65, 0x78,
	0x74, 0x72, 0x61, 0x41, 0x72, 0x67, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x65, 0x65, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x66, 0x65, 0x65, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x65, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0e,
	0x66, 0x65, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x26,
	0x0a, 0x0f, 0x66, 0x65, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x6a, 0x75, 0x65, 0x6c,
	0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x66, 0x65, 0x65, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x4a, 0x75, 0x65, 0x6c, 0x73, 0x12, 0x49, 0x0a, 0x0d, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x61, 0x6d, 0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x22, 0xa2, 0x02, 0x0a, 0x11, 0x52, 0x61, 0x6d, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x15, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x13, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x68, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x2e, 0x0a, 0x13, 0x64, 0x65,
	0x73, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x64, 0x65, 0x73, 0x74, 0x43, 0x68, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0e, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x05, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x73, 0x67,
	0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x6d, 0x73, 0x67,
	0x48, 0x61, 0x73, 0x68, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x6e, 0x5f, 0x72, 0x61, 0x6d, 0x70, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x6f, 0x6e, 0x52, 0x61, 0x6d, 0x70, 0x12, 0x17, 0x0a,
	0x07, 0x74, 0x78, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x74, 0x78, 0x48, 0x61, 0x73, 0x68, 0x22, 0xcc, 0x01, 0x0a, 0x0f, 0x52, 0x61, 0x6d, 0x70, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x11, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50,
	0x6f, 0x6f, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x64, 0x65,
	0x73, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x10, 0x64, 0x65, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x65, 0x78,
	0x74, 0x72, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x24, 0x0a, 0x0e, 0x64, 0x65, 0x73, 0x74, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x64, 0x65, 0x73, 0x74, 0x45, 0x78, 0x65,
	0x63, 0x44, 0x61, 0x74, 0x61, 0x22, 0x9a, 0x01, 0x0a, 0x11, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x41, 0x64, 0x64, 0x72, 0x54, 0x6f, 0x4e, 0x6f, 0x6e, 0x63, 0x65, 0x12, 0x4a, 0x0a, 0x06, 0x6e,
	0x6f, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x70, 0x6b,
	0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72, 0x54, 0x6f, 0x4e, 0x6f,
	0x6e, 0x63, 0x65, 0x2e, 0x4e, 0x6f, 0x6e, 0x63, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x06, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4e, 0x6f, 0x6e, 0x63, 0x65,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x5c, 0x0a, 0x13, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x45, 0x0a, 0x0d, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f,
	0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x0c, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73,
	0x22, 0x8f, 0x02, 0x0a, 0x0b, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x12, 0x32, 0x0a, 0x15, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x13, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x12, 0x38, 0x0a, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x6f, 0x63, 0x72,
	0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x52, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x12, 0x52,
	0x0a, 0x13, 0x6f, 0x66, 0x66, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x70, 0x6b,
	0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x74, 0x65, 0x73, 0x52,
	0x11, 0x6f, 0x66, 0x66, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x6f, 0x6f, 0x66, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0c, 0x52, 0x06, 0x70, 0x72, 0x6f, 0x6f, 0x66, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x72,
	0x6f, 0x6f, 0x66, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x5f, 0x62, 0x69, 0x74, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x6f, 0x66, 0x46, 0x6c, 0x61, 0x67, 0x42, 0x69,
	0x74, 0x73, 0x22, 0x25, 0x0a, 0x0d, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79,
	0x74, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0c, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x49, 0x0a, 0x0b, 0x53, 0x65, 0x71,
	0x4e, 0x75, 0x6d, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f,
	0x6d, 0x73, 0x67, 0x5f, 0x6e, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x6d, 0x69,
	0x6e, 0x4d, 0x73, 0x67, 0x4e, 0x72, 0x12, 0x1c, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x6d, 0x73,
	0x67, 0x5f, 0x6e, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x4d,
	0x73, 0x67, 0x4e, 0x72, 0x22, 0x43, 0x0a, 0x0b, 0x53, 0x65, 0x71, 0x4e, 0x75, 0x6d, 0x43, 0x68,
	0x61, 0x69, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x73, 0x65, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x6c,
	0x12, 0x17, 0x0a, 0x07, 0x73, 0x65, 0x71, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x06, 0x73, 0x65, 0x71, 0x4e, 0x75, 0x6d, 0x22, 0x6f, 0x0a, 0x0a, 0x43, 0x68, 0x61,
	0x69, 0x6e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x5f, 0x73, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x6c, 0x12, 0x44, 0x0a, 0x0d, 0x73, 0x65, 0x71, 0x5f, 0x6e, 0x75, 0x6d, 0x5f,
	0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x6b,
	0x67, 0x2e, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x71, 0x4e, 0x75, 0x6d, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0b, 0x73,
	0x65, 0x71, 0x4e, 0x75, 0x6d, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x22, 0x6c, 0x0a, 0x0f, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x32, 0x0a,
	0x15, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x73, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x13, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x6e, 0x72, 0x61, 0x6d, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x6f, 0x6e, 0x72, 0x61, 0x6d,
	0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0xbf, 0x01, 0x0a, 0x0f, 0x4d, 0x65, 0x72,
	0x6b, 0x6c, 0x65, 0x52, 0x6f, 0x6f, 0x74, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x73, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x08, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x6c, 0x12, 0x26, 0x0a, 0x0f, 0x6f, 0x6e, 0x5f,
	0x72, 0x61, 0x6d, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x0d, 0x6f, 0x6e, 0x52, 0x61, 0x6d, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x12, 0x46, 0x0a, 0x0e, 0x73, 0x65, 0x71, 0x5f, 0x6e, 0x75, 0x6d, 0x73, 0x5f, 0x72, 0x61,
	0x6e, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x6b, 0x67, 0x2e,
	0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x71, 0x4e, 0x75, 0x6d, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0c, 0x73, 0x65, 0x71,
	0x4e, 0x75, 0x6d, 0x73, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72,
	0x6b, 0x6c, 0x65, 0x5f, 0x72, 0x6f, 0x6f, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a,
	0x6d, 0x65, 0x72, 0x6b, 0x6c, 0x65, 0x52, 0x6f, 0x6f, 0x74, 0x22, 0x60, 0x0a, 0x0e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x65, 0x64, 0x42, 0x69, 0x67, 0x12, 0x38, 0x0a, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x13, 0x5a, 0x11,
	0x2e, 0x2f, 0x3b, 0x6f, 0x63, 0x72, 0x74, 0x79, 0x70, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescOnce sync.Once
	file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescData = file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDesc
)

func file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescGZIP() []byte {
	file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescOnce.Do(func() {
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescData = protoimpl.X.CompressGZIP(file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescData)
	})
	return file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDescData
}

var file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes = make([]protoimpl.MessageInfo, 71)
var file_pkg_ocrtypecodec_v1_ocrtypes_proto_goTypes = []interface{}{
	(*CommitQuery)(nil),                // 0: pkg.ocrtypecodec.v1.CommitQuery
	(*CommitObservation)(nil),          // 1: pkg.ocrtypecodec.v1.CommitObservation
	(*CommitOutcome)(nil),              // 2: pkg.ocrtypecodec.v1.CommitOutcome
	(*ExecObservation)(nil),            // 3: pkg.ocrtypecodec.v1.ExecObservation
	(*ExecOutcome)(nil),                // 4: pkg.ocrtypecodec.v1.ExecOutcome
	(*MerkleRootQuery)(nil),            // 5: pkg.ocrtypecodec.v1.MerkleRootQuery
	(*ReportSignatures)(nil),           // 6: pkg.ocrtypecodec.v1.ReportSignatures
	(*SignatureEcdsa)(nil),             // 7: pkg.ocrtypecodec.v1.SignatureEcdsa
	(*DestChainUpdate)(nil),            // 8: pkg.ocrtypecodec.v1.DestChainUpdate
	(*MerkleRootObservation)(nil),      // 9: pkg.ocrtypecodec.v1.MerkleRootObservation
	(*RmnRemoteConfig)(nil),            // 10: pkg.ocrtypecodec.v1.RmnRemoteConfig
	(*RemoteSignerInfo)(nil),           // 11: pkg.ocrtypecodec.v1.RemoteSignerInfo
	(*TokenPriceObservation)(nil),      // 12: pkg.ocrtypecodec.v1.TokenPriceObservation
	(*ChainFeeObservation)(nil),        // 13: pkg.ocrtypecodec.v1.ChainFeeObservation
	(*ChainFeeComponents)(nil),         // 14: pkg.ocrtypecodec.v1.ChainFeeComponents
	(*ChainFeeUpdate)(nil),             // 15: pkg.ocrtypecodec.v1.ChainFeeUpdate
	(*ComponentsUSDPrices)(nil),        // 16: pkg.ocrtypecodec.v1.ComponentsUSDPrices
	(*DiscoveryObservation)(nil),       // 17: pkg.ocrtypecodec.v1.DiscoveryObservation
	(*ContractNameChainAddresses)(nil), // 18: pkg.ocrtypecodec.v1.ContractNameChainAddresses
	(*ChainAddressMap)(nil),            // 19: pkg.ocrtypecodec.v1.ChainAddressMap
	(*MerkleRootOutcome)(nil),          // 20: pkg.ocrtypecodec.v1.MerkleRootOutcome
	(*TokenPriceOutcome)(nil),          // 21: pkg.ocrtypecodec.v1.TokenPriceOutcome
	(*ChainFeeOutcome)(nil),            // 22: pkg.ocrtypecodec.v1.ChainFeeOutcome
	(*GasPriceChain)(nil),              // 23: pkg.ocrtypecodec.v1.GasPriceChain
	(*MainOutcome)(nil),                // 24: pkg.ocrtypecodec.v1.MainOutcome
	(*CommitObservations)(nil),         // 25: pkg.ocrtypecodec.v1.CommitObservations
	(*CommitData)(nil),                 // 26: pkg.ocrtypecodec.v1.CommitData
	(*MessageTokenData)(nil),           // 27: pkg.ocrtypecodec.v1.MessageTokenData
	(*TokenData)(nil),                  // 28: pkg.ocrtypecodec.v1.TokenData
	(*SeqNumToMessage)(nil),            // 29: pkg.ocrtypecodec.v1.SeqNumToMessage
	(*SeqNumToBytes)(nil),              // 30: pkg.ocrtypecodec.v1.SeqNumToBytes
	(*TokenDataObservations)(nil),      // 31: pkg.ocrtypecodec.v1.TokenDataObservations
	(*SeqNumToTokenData)(nil),          // 32: pkg.ocrtypecodec.v1.SeqNumToTokenData
	(*Message)(nil),                    // 33: pkg.ocrtypecodec.v1.Message
	(*RampMessageHeader)(nil),          // 34: pkg.ocrtypecodec.v1.RampMessageHeader
	(*RampTokenAmount)(nil),            // 35: pkg.ocrtypecodec.v1.RampTokenAmount
	(*StringAddrToNonce)(nil),          // 36: pkg.ocrtypecodec.v1.StringAddrToNonce
	(*ExecutePluginReport)(nil),        // 37: pkg.ocrtypecodec.v1.ExecutePluginReport
	(*ChainReport)(nil),                // 38: pkg.ocrtypecodec.v1.ChainReport
	(*RepeatedBytes)(nil),              // 39: pkg.ocrtypecodec.v1.RepeatedBytes
	(*SeqNumRange)(nil),                // 40: pkg.ocrtypecodec.v1.SeqNumRange
	(*SeqNumChain)(nil),                // 41: pkg.ocrtypecodec.v1.SeqNumChain
	(*ChainRange)(nil),                 // 42: pkg.ocrtypecodec.v1.ChainRange
	(*SourceChainMeta)(nil),            // 43: pkg.ocrtypecodec.v1.SourceChainMeta
	(*MerkleRootChain)(nil),            // 44: pkg.ocrtypecodec.v1.MerkleRootChain
	(*TimestampedBig)(nil),             // 45: pkg.ocrtypecodec.v1.TimestampedBig
	nil,                                // 46: pkg.ocrtypecodec.v1.CommitObservation.FChainEntry
	nil,                                // 47: pkg.ocrtypecodec.v1.ExecObservation.CommitReportsEntry
	nil,                                // 48: pkg.ocrtypecodec.v1.ExecObservation.SeqNumsToMsgsEntry
	nil,                                // 49: pkg.ocrtypecodec.v1.ExecObservation.MsgHashesEntry
	nil,                                // 50: pkg.ocrtypecodec.v1.ExecObservation.NoncesEntry
	nil,                                // 51: pkg.ocrtypecodec.v1.ExecObservation.FChainEntry
	nil,                                // 52: pkg.ocrtypecodec.v1.MerkleRootObservation.RmnEnabledChainsEntry
	nil,                                // 53: pkg.ocrtypecodec.v1.MerkleRootObservation.FChainEntry
	nil,                                // 54: pkg.ocrtypecodec.v1.TokenPriceObservation.FeedTokenPricesEntry
	nil,                                // 55: pkg.ocrtypecodec.v1.TokenPriceObservation.FeeQuoterTokenUpdatesEntry
	nil,                                // 56: pkg.ocrtypecodec.v1.TokenPriceObservation.FChainEntry
	nil,                                // 57: pkg.ocrtypecodec.v1.ChainFeeObservation.FeeComponentsEntry
	nil,                                // 58: pkg.ocrtypecodec.v1.ChainFeeObservation.NativeTokenPricesEntry
	nil,                                // 59: pkg.ocrtypecodec.v1.ChainFeeObservation.ChainFeeUpdatesEntry
	nil,                                // 60: pkg.ocrtypecodec.v1.ChainFeeObservation.FChainEntry
	nil,                                // 61: pkg.ocrtypecodec.v1.DiscoveryObservation.FChainEntry
	nil,                                // 62: pkg.ocrtypecodec.v1.ContractNameChainAddresses.AddressesEntry
	nil,                                // 63: pkg.ocrtypecodec.v1.ChainAddressMap.ChainAddressesEntry
	nil,                                // 64: pkg.ocrtypecodec.v1.MerkleRootOutcome.RmnEnabledChainsEntry
	nil,                                // 65: pkg.ocrtypecodec.v1.TokenPriceOutcome.TokenPricesEntry
	nil,                                // 66: pkg.ocrtypecodec.v1.SeqNumToMessage.MessagesEntry
	nil,                                // 67: pkg.ocrtypecodec.v1.SeqNumToBytes.SeqNumToBytesEntry
	nil,                                // 68: pkg.ocrtypecodec.v1.TokenDataObservations.TokenDataEntry
	nil,                                // 69: pkg.ocrtypecodec.v1.SeqNumToTokenData.TokenDataEntry
	nil,                                // 70: pkg.ocrtypecodec.v1.StringAddrToNonce.NoncesEntry
	(*timestamppb.Timestamp)(nil),      // 71: google.protobuf.Timestamp
}
var file_pkg_ocrtypecodec_v1_ocrtypes_proto_depIdxs = []int32{
	5,  // 0: pkg.ocrtypecodec.v1.CommitQuery.merkle_root_query:type_name -> pkg.ocrtypecodec.v1.MerkleRootQuery
	9,  // 1: pkg.ocrtypecodec.v1.CommitObservation.merkle_root_obs:type_name -> pkg.ocrtypecodec.v1.MerkleRootObservation
	12, // 2: pkg.ocrtypecodec.v1.CommitObservation.token_price_obs:type_name -> pkg.ocrtypecodec.v1.TokenPriceObservation
	13, // 3: pkg.ocrtypecodec.v1.CommitObservation.chain_fee_obs:type_name -> pkg.ocrtypecodec.v1.ChainFeeObservation
	17, // 4: pkg.ocrtypecodec.v1.CommitObservation.discovery_obs:type_name -> pkg.ocrtypecodec.v1.DiscoveryObservation
	46, // 5: pkg.ocrtypecodec.v1.CommitObservation.f_chain:type_name -> pkg.ocrtypecodec.v1.CommitObservation.FChainEntry
	20, // 6: pkg.ocrtypecodec.v1.CommitOutcome.merkle_root_outcome:type_name -> pkg.ocrtypecodec.v1.MerkleRootOutcome
	21, // 7: pkg.ocrtypecodec.v1.CommitOutcome.token_price_outcome:type_name -> pkg.ocrtypecodec.v1.TokenPriceOutcome
	22, // 8: pkg.ocrtypecodec.v1.CommitOutcome.chain_fee_outcome:type_name -> pkg.ocrtypecodec.v1.ChainFeeOutcome
	24, // 9: pkg.ocrtypecodec.v1.CommitOutcome.main_outcome:type_name -> pkg.ocrtypecodec.v1.MainOutcome
	47, // 10: pkg.ocrtypecodec.v1.ExecObservation.commit_reports:type_name -> pkg.ocrtypecodec.v1.ExecObservation.CommitReportsEntry
	48, // 11: pkg.ocrtypecodec.v1.ExecObservation.seq_nums_to_msgs:type_name -> pkg.ocrtypecodec.v1.ExecObservation.SeqNumsToMsgsEntry
	49, // 12: pkg.ocrtypecodec.v1.ExecObservation.msg_hashes:type_name -> pkg.ocrtypecodec.v1.ExecObservation.MsgHashesEntry
	31, // 13: pkg.ocrtypecodec.v1.ExecObservation.token_data_observations:type_name -> pkg.ocrtypecodec.v1.TokenDataObservations
	50, // 14: pkg.ocrtypecodec.v1.ExecObservation.nonces:type_name -> pkg.ocrtypecodec.v1.ExecObservation.NoncesEntry
	17, // 15: pkg.ocrtypecodec.v1.ExecObservation.contracts:type_name -> pkg.ocrtypecodec.v1.DiscoveryObservation
	51, // 16: pkg.ocrtypecodec.v1.ExecObservation.f_chain:type_name -> pkg.ocrtypecodec.v1.ExecObservation.FChainEntry
	26, // 17: pkg.ocrtypecodec.v1.ExecOutcome.commit_reports:type_name -> pkg.ocrtypecodec.v1.CommitData
	37, // 18: pkg.ocrtypecodec.v1.ExecOutcome.execute_plugin_report:type_name -> pkg.ocrtypecodec.v1.ExecutePluginReport
	37, // 19: pkg.ocrtypecodec.v1.ExecOutcome.execute_plugin_reports:type_name -> pkg.ocrtypecodec.v1.ExecutePluginReport
	6,  // 20: pkg.ocrtypecodec.v1.MerkleRootQuery.rmn_signatures:type_name -> pkg.ocrtypecodec.v1.ReportSignatures
	7,  // 21: pkg.ocrtypecodec.v1.ReportSignatures.signatures:type_name -> pkg.ocrtypecodec.v1.SignatureEcdsa
	8,  // 22: pkg.ocrtypecodec.v1.ReportSignatures.lane_updates:type_name -> pkg.ocrtypecodec.v1.DestChainUpdate
	43, // 23: pkg.ocrtypecodec.v1.DestChainUpdate.lane_source:type_name -> pkg.ocrtypecodec.v1.SourceChainMeta
	40, // 24: pkg.ocrtypecodec.v1.DestChainUpdate.seq_num_range:type_name -> pkg.ocrtypecodec.v1.SeqNumRange
	44, // 25: pkg.ocrtypecodec.v1.MerkleRootObservation.merkle_roots:type_name -> pkg.ocrtypecodec.v1.MerkleRootChain
	52, // 26: pkg.ocrtypecodec.v1.MerkleRootObservation.rmn_enabled_chains:type_name -> pkg.ocrtypecodec.v1.MerkleRootObservation.RmnEnabledChainsEntry
	41, // 27: pkg.ocrtypecodec.v1.MerkleRootObservation.on_ramp_max_seq_nums:type_name -> pkg.ocrtypecodec.v1.SeqNumChain
	41, // 28: pkg.ocrtypecodec.v1.MerkleRootObservation.off_ramp_next_seq_nums:type_name -> pkg.ocrtypecodec.v1.SeqNumChain
	10, // 29: pkg.ocrtypecodec.v1.MerkleRootObservation.rmn_remote_config:type_name -> pkg.ocrtypecodec.v1.RmnRemoteConfig
	53, // 30: pkg.ocrtypecodec.v1.MerkleRootObservation.f_chain:type_name -> pkg.ocrtypecodec.v1.MerkleRootObservation.FChainEntry
	11, // 31: pkg.ocrtypecodec.v1.RmnRemoteConfig.signers:type_name -> pkg.ocrtypecodec.v1.RemoteSignerInfo
	54, // 32: pkg.ocrtypecodec.v1.TokenPriceObservation.feed_token_prices:type_name -> pkg.ocrtypecodec.v1.TokenPriceObservation.FeedTokenPricesEntry
	55, // 33: pkg.ocrtypecodec.v1.TokenPriceObservation.fee_quoter_token_updates:type_name -> pkg.ocrtypecodec.v1.TokenPriceObservation.FeeQuoterTokenUpdatesEntry
	56, // 34: pkg.ocrtypecodec.v1.TokenPriceObservation.f_chain:type_name -> pkg.ocrtypecodec.v1.TokenPriceObservation.FChainEntry
	71, // 35: pkg.ocrtypecodec.v1.TokenPriceObservation.timestamp:type_name -> google.protobuf.Timestamp
	57, // 36: pkg.ocrtypecodec.v1.ChainFeeObservation.fee_components:type_name -> pkg.ocrtypecodec.v1.ChainFeeObservation.FeeComponentsEntry
	58, // 37: pkg.ocrtypecodec.v1.ChainFeeObservation.native_token_prices:type_name -> pkg.ocrtypecodec.v1.ChainFeeObservation.NativeTokenPricesEntry
	59, // 38: pkg.ocrtypecodec.v1.ChainFeeObservation.chain_fee_updates:type_name -> pkg.ocrtypecodec.v1.ChainFeeObservation.ChainFeeUpdatesEntry
	60, // 39: pkg.ocrtypecodec.v1.ChainFeeObservation.f_chain:type_name -> pkg.ocrtypecodec.v1.ChainFeeObservation.FChainEntry
	71, // 40: pkg.ocrtypecodec.v1.ChainFeeObservation.timestamp_now:type_name -> google.protobuf.Timestamp
	16, // 41: pkg.ocrtypecodec.v1.ChainFeeUpdate.chain_fee:type_name -> pkg.ocrtypecodec.v1.ComponentsUSDPrices
	71, // 42: pkg.ocrtypecodec.v1.ChainFeeUpdate.timestamp:type_name -> google.protobuf.Timestamp
	61, // 43: pkg.ocrtypecodec.v1.DiscoveryObservation.f_chain:type_name -> pkg.ocrtypecodec.v1.DiscoveryObservation.FChainEntry
	18, // 44: pkg.ocrtypecodec.v1.DiscoveryObservation.contract_names:type_name -> pkg.ocrtypecodec.v1.ContractNameChainAddresses
	62, // 45: pkg.ocrtypecodec.v1.ContractNameChainAddresses.addresses:type_name -> pkg.ocrtypecodec.v1.ContractNameChainAddresses.AddressesEntry
	63, // 46: pkg.ocrtypecodec.v1.ChainAddressMap.chain_addresses:type_name -> pkg.ocrtypecodec.v1.ChainAddressMap.ChainAddressesEntry
	42, // 47: pkg.ocrtypecodec.v1.MerkleRootOutcome.ranges_selected_for_report:type_name -> pkg.ocrtypecodec.v1.ChainRange
	44, // 48: pkg.ocrtypecodec.v1.MerkleRootOutcome.roots_to_report:type_name -> pkg.ocrtypecodec.v1.MerkleRootChain
	64, // 49: pkg.ocrtypecodec.v1.MerkleRootOutcome.rmn_enabled_chains:type_name -> pkg.ocrtypecodec.v1.MerkleRootOutcome.RmnEnabledChainsEntry
	41, // 50: pkg.ocrtypecodec.v1.MerkleRootOutcome.off_ramp_next_seq_nums:type_name -> pkg.ocrtypecodec.v1.SeqNumChain
	7,  // 51: pkg.ocrtypecodec.v1.MerkleRootOutcome.rmn_report_signatures:type_name -> pkg.ocrtypecodec.v1.SignatureEcdsa
	10, // 52: pkg.ocrtypecodec.v1.MerkleRootOutcome.rmn_remote_cfg:type_name -> pkg.ocrtypecodec.v1.RmnRemoteConfig
	65, // 53: pkg.ocrtypecodec.v1.TokenPriceOutcome.token_prices:type_name -> pkg.ocrtypecodec.v1.TokenPriceOutcome.TokenPricesEntry
	23, // 54: pkg.ocrtypecodec.v1.ChainFeeOutcome.gas_prices:type_name -> pkg.ocrtypecodec.v1.GasPriceChain
	26, // 55: pkg.ocrtypecodec.v1.CommitObservations.commit_data:type_name -> pkg.ocrtypecodec.v1.CommitData
	71, // 56: pkg.ocrtypecodec.v1.CommitData.timestamp:type_name -> google.protobuf.Timestamp
	40, // 57: pkg.ocrtypecodec.v1.CommitData.sequence_number_range:type_name -> pkg.ocrtypecodec.v1.SeqNumRange
	33, // 58: pkg.ocrtypecodec.v1.CommitData.messages:type_name -> pkg.ocrtypecodec.v1.Message
	27, // 59: pkg.ocrtypecodec.v1.CommitData.message_token_data:type_name -> pkg.ocrtypecodec.v1.MessageTokenData
	28, // 60: pkg.ocrtypecodec.v1.MessageTokenData.token_data:type_name -> pkg.ocrtypecodec.v1.TokenData
	66, // 61: pkg.ocrtypecodec.v1.SeqNumToMessage.messages:type_name -> pkg.ocrtypecodec.v1.SeqNumToMessage.MessagesEntry
	67, // 62: pkg.ocrtypecodec.v1.SeqNumToBytes.seq_num_to_bytes:type_name -> pkg.ocrtypecodec.v1.SeqNumToBytes.SeqNumToBytesEntry
	68, // 63: pkg.ocrtypecodec.v1.TokenDataObservations.token_data:type_name -> pkg.ocrtypecodec.v1.TokenDataObservations.TokenDataEntry
	69, // 64: pkg.ocrtypecodec.v1.SeqNumToTokenData.token_data:type_name -> pkg.ocrtypecodec.v1.SeqNumToTokenData.TokenDataEntry
	34, // 65: pkg.ocrtypecodec.v1.Message.header:type_name -> pkg.ocrtypecodec.v1.RampMessageHeader
	35, // 66: pkg.ocrtypecodec.v1.Message.token_amounts:type_name -> pkg.ocrtypecodec.v1.RampTokenAmount
	70, // 67: pkg.ocrtypecodec.v1.StringAddrToNonce.nonces:type_name -> pkg.ocrtypecodec.v1.StringAddrToNonce.NoncesEntry
	38, // 68: pkg.ocrtypecodec.v1.ExecutePluginReport.chain_reports:type_name -> pkg.ocrtypecodec.v1.ChainReport
	33, // 69: pkg.ocrtypecodec.v1.ChainReport.messages:type_name -> pkg.ocrtypecodec.v1.Message
	39, // 70: pkg.ocrtypecodec.v1.ChainReport.offchain_token_data:type_name -> pkg.ocrtypecodec.v1.RepeatedBytes
	40, // 71: pkg.ocrtypecodec.v1.ChainRange.seq_num_range:type_name -> pkg.ocrtypecodec.v1.SeqNumRange
	40, // 72: pkg.ocrtypecodec.v1.MerkleRootChain.seq_nums_range:type_name -> pkg.ocrtypecodec.v1.SeqNumRange
	71, // 73: pkg.ocrtypecodec.v1.TimestampedBig.timestamp:type_name -> google.protobuf.Timestamp
	25, // 74: pkg.ocrtypecodec.v1.ExecObservation.CommitReportsEntry.value:type_name -> pkg.ocrtypecodec.v1.CommitObservations
	29, // 75: pkg.ocrtypecodec.v1.ExecObservation.SeqNumsToMsgsEntry.value:type_name -> pkg.ocrtypecodec.v1.SeqNumToMessage
	30, // 76: pkg.ocrtypecodec.v1.ExecObservation.MsgHashesEntry.value:type_name -> pkg.ocrtypecodec.v1.SeqNumToBytes
	36, // 77: pkg.ocrtypecodec.v1.ExecObservation.NoncesEntry.value:type_name -> pkg.ocrtypecodec.v1.StringAddrToNonce
	45, // 78: pkg.ocrtypecodec.v1.TokenPriceObservation.FeeQuoterTokenUpdatesEntry.value:type_name -> pkg.ocrtypecodec.v1.TimestampedBig
	14, // 79: pkg.ocrtypecodec.v1.ChainFeeObservation.FeeComponentsEntry.value:type_name -> pkg.ocrtypecodec.v1.ChainFeeComponents
	15, // 80: pkg.ocrtypecodec.v1.ChainFeeObservation.ChainFeeUpdatesEntry.value:type_name -> pkg.ocrtypecodec.v1.ChainFeeUpdate
	19, // 81: pkg.ocrtypecodec.v1.ContractNameChainAddresses.AddressesEntry.value:type_name -> pkg.ocrtypecodec.v1.ChainAddressMap
	33, // 82: pkg.ocrtypecodec.v1.SeqNumToMessage.MessagesEntry.value:type_name -> pkg.ocrtypecodec.v1.Message
	32, // 83: pkg.ocrtypecodec.v1.TokenDataObservations.TokenDataEntry.value:type_name -> pkg.ocrtypecodec.v1.SeqNumToTokenData
	27, // 84: pkg.ocrtypecodec.v1.SeqNumToTokenData.TokenDataEntry.value:type_name -> pkg.ocrtypecodec.v1.MessageTokenData
	85, // [85:85] is the sub-list for method output_type
	85, // [85:85] is the sub-list for method input_type
	85, // [85:85] is the sub-list for extension type_name
	85, // [85:85] is the sub-list for extension extendee
	0,  // [0:85] is the sub-list for field type_name
}

func init() { file_pkg_ocrtypecodec_v1_ocrtypes_proto_init() }
func file_pkg_ocrtypecodec_v1_ocrtypes_proto_init() {
	if File_pkg_ocrtypecodec_v1_ocrtypes_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommitQuery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommitObservation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommitOutcome); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecObservation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecOutcome); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MerkleRootQuery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportSignatures); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignatureEcdsa); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DestChainUpdate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MerkleRootObservation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RmnRemoteConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoteSignerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TokenPriceObservation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChainFeeObservation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChainFeeComponents); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChainFeeUpdate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ComponentsUSDPrices); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DiscoveryObservation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContractNameChainAddresses); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChainAddressMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MerkleRootOutcome); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TokenPriceOutcome); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChainFeeOutcome); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GasPriceChain); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MainOutcome); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommitObservations); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommitData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageTokenData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TokenData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeqNumToMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeqNumToBytes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TokenDataObservations); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeqNumToTokenData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Message); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RampMessageHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RampTokenAmount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StringAddrToNonce); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecutePluginReport); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChainReport); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RepeatedBytes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeqNumRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeqNumChain); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChainRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SourceChainMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MerkleRootChain); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimestampedBig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   71,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pkg_ocrtypecodec_v1_ocrtypes_proto_goTypes,
		DependencyIndexes: file_pkg_ocrtypecodec_v1_ocrtypes_proto_depIdxs,
		MessageInfos:      file_pkg_ocrtypecodec_v1_ocrtypes_proto_msgTypes,
	}.Build()
	File_pkg_ocrtypecodec_v1_ocrtypes_proto = out.File
	file_pkg_ocrtypecodec_v1_ocrtypes_proto_rawDesc = nil
	file_pkg_ocrtypecodec_v1_ocrtypes_proto_goTypes = nil
	file_pkg_ocrtypecodec_v1_ocrtypes_proto_depIdxs = nil
}
