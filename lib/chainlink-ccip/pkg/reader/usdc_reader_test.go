package reader

import (
	"errors"
	"fmt"
	"maps"
	"slices"
	"testing"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/smartcontractkit/chainlink-ccip/internal"

	"github.com/smartcontractkit/chainlink-common/pkg/logger"

	sel "github.com/smartcontractkit/chain-selectors"

	"github.com/smartcontractkit/chainlink-common/pkg/types"
	cciptypes "github.com/smartcontractkit/chainlink-common/pkg/types/ccipocr3"

	mockChainAccessor "github.com/smartcontractkit/chainlink-ccip/mocks/chainlink_common/ccipocr3"
	reader "github.com/smartcontractkit/chainlink-ccip/mocks/pkg/contractreader"
	"github.com/smartcontractkit/chainlink-ccip/pkg/consts"
	"github.com/smartcontractkit/chainlink-ccip/pkg/contractreader"
	"github.com/smartcontractkit/chainlink-ccip/pluginconfig"
)

func Test_USDCMessageReader_New(t *testing.T) {
	address1 := "0x0000000000000000000000000000000000000001"
	address2 := "******************************************"

	emptyReaders := func() map[cciptypes.ChainSelector]*reader.MockExtended {
		return map[cciptypes.ChainSelector]*reader.MockExtended{}
	}

	tt := []struct {
		name         string
		tokensConfig map[cciptypes.ChainSelector]pluginconfig.USDCCCTPTokenConfig
		readers      func() map[cciptypes.ChainSelector]*reader.MockExtended
		errorMessage string
	}{
		{
			name:         "empty tokens and readers works",
			tokensConfig: map[cciptypes.ChainSelector]pluginconfig.USDCCCTPTokenConfig{},
			readers:      emptyReaders,
		},
		{
			name: "unknown chain family",
			tokensConfig: map[cciptypes.ChainSelector]pluginconfig.USDCCCTPTokenConfig{
				cciptypes.ChainSelector(1): {},
			},
			readers:      emptyReaders,
			errorMessage: "failed to get selector family for chain 1: unknown chain selector 1",
		},
		{
			name: "missing readers doesn't fail",
			tokensConfig: map[cciptypes.ChainSelector]pluginconfig.USDCCCTPTokenConfig{
				cciptypes.ChainSelector(sel.ETHEREUM_TESTNET_SEPOLIA.Selector): {
					SourcePoolAddress:            address1,
					SourceMessageTransmitterAddr: address2,
				},
			},
			readers: emptyReaders,
		},
		{
			name: "binding errors",
			tokensConfig: map[cciptypes.ChainSelector]pluginconfig.USDCCCTPTokenConfig{
				cciptypes.ChainSelector(sel.ETHEREUM_TESTNET_SEPOLIA.Selector): {
					SourcePoolAddress:            address1,
					SourceMessageTransmitterAddr: address2,
				},
			},
			readers: func() map[cciptypes.ChainSelector]*reader.MockExtended {
				readers := make(map[cciptypes.ChainSelector]*reader.MockExtended)
				m := reader.NewMockExtended(t)
				m.EXPECT().Bind(mock.Anything, mock.Anything).Return(errors.New("error"))
				cs := cciptypes.ChainSelector(sel.ETHEREUM_TESTNET_SEPOLIA.Selector)
				readers[cs] = m
				return readers
			},
			errorMessage: "unable to bind MessageTransmitter ****************************************** for chain 1",
		},
		{
			name: "happy path",
			tokensConfig: map[cciptypes.ChainSelector]pluginconfig.USDCCCTPTokenConfig{
				cciptypes.ChainSelector(sel.ETHEREUM_TESTNET_SEPOLIA.Selector): {
					SourcePoolAddress:            address1,
					SourceMessageTransmitterAddr: address2,
				},
			},
			readers: func() map[cciptypes.ChainSelector]*reader.MockExtended {
				readers := make(map[cciptypes.ChainSelector]*reader.MockExtended)
				m := reader.NewMockExtended(t)
				m.EXPECT().Bind(mock.Anything, []types.BoundContract{
					{
						Address: address2,
						Name:    consts.ContractNameCCTPMessageTransmitter,
					},
				}).Return(nil)

				cs := cciptypes.ChainSelector(sel.ETHEREUM_TESTNET_SEPOLIA.Selector)
				readers[cs] = m
				return readers
			},
		},
	}

	mockAddrCodec := internal.NewMockAddressCodecHex(t)
	for _, tc := range tt {
		t.Run(tc.name, func(t *testing.T) {
			ctx := t.Context()
			readers := make(map[cciptypes.ChainSelector]contractreader.Extended)
			for k, v := range tc.readers() {
				readers[k] = v
			}

			emptyChainAccessors := make(map[cciptypes.ChainSelector]cciptypes.ChainAccessor)
			r, err := NewUSDCMessageReader(
				ctx,
				logger.Test(t),
				tc.tokensConfig,
				make(map[string]bool),
				emptyChainAccessors,
				readers,
				mockAddrCodec,
			)
			if tc.errorMessage != "" {
				require.Error(t, err)
				require.Contains(t, err.Error(), tc.errorMessage)
			} else {
				require.NoError(t, err)
				require.NotNil(t, r)
			}
		})
	}
}

func Test_USDCMessageReader_MessagesByTokenID(t *testing.T) {
	ctx := t.Context()
	emptyChain := cciptypes.ChainSelector(sel.ETHEREUM_MAINNET.Selector)
	emptyReader := reader.NewMockExtended(t)
	emptyReader.EXPECT().Bind(mock.Anything, mock.Anything).Return(nil)
	emptyReader.EXPECT().ExtendedQueryKey(
		mock.Anything,
		mock.Anything,
		mock.Anything,
		mock.Anything,
		mock.Anything,
	).Return([]types.Sequence{}, nil).Maybe()

	faultyChain := cciptypes.ChainSelector(sel.AVALANCHE_MAINNET.Selector)
	faultyReader := reader.NewMockExtended(t)
	faultyReader.EXPECT().Bind(mock.Anything, mock.Anything).Return(nil)
	faultyReader.EXPECT().ExtendedQueryKey(
		mock.Anything,
		mock.Anything,
		mock.Anything,
		mock.Anything,
		mock.Anything,
	).Return(nil, errors.New("error")).Maybe()

	validSequence := []types.Sequence{
		{
			Data: &MessageSentEvent{Arg0: make([]byte, 128)},
		},
	}

	validChain := cciptypes.ChainSelector(sel.ETHEREUM_MAINNET_ARBITRUM_1.Selector)
	validChainCCTP := CCTPDestDomains[uint64(validChain)]
	validReader := reader.NewMockExtended(t)
	validReader.EXPECT().Bind(mock.Anything, mock.Anything).Return(nil)
	validReader.EXPECT().ExtendedQueryKey(
		mock.Anything,
		mock.Anything,
		mock.Anything,
		mock.Anything,
		mock.Anything,
	).Return(validSequence, nil).Maybe()

	tokensConfigs := map[cciptypes.ChainSelector]pluginconfig.USDCCCTPTokenConfig{
		faultyChain: {
			SourcePoolAddress:            "******************************************",
			SourceMessageTransmitterAddr: "******************************************",
		},
		emptyChain: {
			SourcePoolAddress:            "******************************************",
			SourceMessageTransmitterAddr: "******************************************",
		},
		validChain: {
			SourcePoolAddress:            "******************************************",
			SourceMessageTransmitterAddr: "******************************************",
		},
	}

	contactReaders := map[cciptypes.ChainSelector]contractreader.Extended{
		faultyChain: faultyReader,
		emptyChain:  emptyReader,
		validChain:  validReader,
	}

	tokens := map[MessageTokenID]cciptypes.RampTokenAmount{
		NewMessageTokenID(1, 1): {
			ExtraData: NewSourceTokenDataPayload(11, validChainCCTP).ToBytes(),
		},
	}

	mockAddrCodec := internal.NewMockAddressCodecHex(t)
	emptyChainAccessors := make(map[cciptypes.ChainSelector]cciptypes.ChainAccessor)
	usdcReader, err := NewUSDCMessageReader(
		ctx,
		logger.Test(t),
		tokensConfigs,
		make(map[string]bool),
		emptyChainAccessors,
		contactReaders,
		mockAddrCodec,
	)
	require.NoError(t, err)

	tt := []struct {
		name           string
		sourceSelector cciptypes.ChainSelector
		destSelector   cciptypes.ChainSelector
		expectedMsgIDs []MessageTokenID
		errorMessage   string
	}{
		{
			name:           "should return empty dataset when chain doesn't have events",
			sourceSelector: emptyChain,
			destSelector:   faultyChain,
			expectedMsgIDs: nil,
		},
		{
			name:           "should return error when chain reader errors",
			sourceSelector: faultyChain,
			destSelector:   emptyChain,
			errorMessage:   fmt.Sprintf("error querying contract reader for chain %d", faultyChain),
		},
		{
			name:           "should return error when CCTP domain is not supported",
			sourceSelector: emptyChain,
			destSelector:   cciptypes.ChainSelector(2),
			errorMessage:   "destination domain not found for chain 2",
		},
		{
			name:           "should return error when CCTP domain is not supported",
			sourceSelector: cciptypes.ChainSelector(sel.POLYGON_MAINNET.Selector),
			destSelector:   emptyChain,
			errorMessage:   fmt.Sprintf("no reader for chain %d", sel.POLYGON_MAINNET.Selector),
		},
		{
			name:           "valid chain return events but nothing is matched",
			sourceSelector: validChain,
			destSelector:   emptyChain,
			expectedMsgIDs: nil,
		},
	}

	for _, tc := range tt {
		t.Run(tc.name, func(t *testing.T) {
			messages, err1 := usdcReader.MessagesByTokenID(
				t.Context(),
				tc.sourceSelector,
				tc.destSelector,
				tokens,
			)

			if tc.errorMessage != "" {
				require.Error(t, err1)
				require.ErrorContains(t, err1, tc.errorMessage)
			} else {
				require.NoError(t, err)
				require.NotNil(t, messages)
				require.Equal(t, tc.expectedMsgIDs, slices.Collect(maps.Keys(messages)))
			}
		})
	}
}

func Test_MessageSentEvent_unpackID(t *testing.T) {
	nonEmptyEvent := eventID{}
	for i := 0; i < 32; i++ {
		nonEmptyEvent[i] = byte(i)
	}

	fullPayload := make([]byte, 0, 64)
	fullPayload = append(fullPayload, nonEmptyEvent[:]...)
	fullPayload = append(fullPayload, nonEmptyEvent[:]...)

	tt := []struct {
		name    string
		data    []byte
		want    eventID
		wantErr bool
	}{
		{
			name:    "event too short",
			data:    make([]byte, 31),
			wantErr: true,
		},
		{
			name: "event with proper length but empty",
			data: make([]byte, 32),
			want: eventID{},
		},
		{
			name: "event with proper length and data",
			data: fullPayload,
			want: nonEmptyEvent,
		},
	}

	for _, tc := range tt {
		t.Run(tc.name, func(t *testing.T) {
			event := MessageSentEvent{Arg0: tc.data}
			got, err := event.unpackID()

			if !tc.wantErr {
				require.NoError(t, err)
				require.Equal(t, tc.want, got)
			} else {
				require.Error(t, err)
			}
		})

	}
}

func Test_extractABIPayload_ToBytes(t *testing.T) {
	tt := []struct {
		nonce        uint64
		sourceDomain uint32
	}{
		{
			nonce:        0,
			sourceDomain: 0,
		},
		{
			nonce:        2137,
			sourceDomain: 4,
		},
		{
			nonce:        2,
			sourceDomain: 2137,
		},
	}

	for _, tc := range tt {
		t.Run(fmt.Sprintf("nonce=%d,sourceDomain=%d", tc.nonce, tc.sourceDomain), func(t *testing.T) {
			payload1 := NewSourceTokenDataPayload(tc.nonce, tc.sourceDomain)
			bytes := payload1.ToBytes()

			payload2, err := extractABIPayload(bytes)
			require.NoError(t, err)
			require.Equal(t, *payload1, *payload2)
		})
	}
}

func Test_extractABIPayload_FromBytes(t *testing.T) {
	tt := []struct {
		name    string
		data    []byte
		want    *SourceTokenDataPayload
		wantErr bool
	}{
		{
			name:    "too short data",
			data:    []byte{0x01, 0x02, 0x03},
			wantErr: true,
		},
		{
			name: "empty data but with proper length",
			data: make([]byte, 64),
			want: NewSourceTokenDataPayload(0, 0),
		},
		{
			name: "data with nonce and source domain",
			data: []byte{
				0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
				0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1,
				0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
				0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2,
			},
			want: NewSourceTokenDataPayload(1, 2),
		},
	}

	for _, tc := range tt {
		t.Run(tc.name, func(t *testing.T) {
			got, err := extractABIPayload(tc.data)

			if !tc.wantErr {
				require.NoError(t, err)
				require.Equal(t, tc.want, got)
			} else {
				require.Error(t, err)
			}
		})
	}
}

func Test_USDCMessageReader_New_SolanaAccessor(t *testing.T) {
	ctx := t.Context()
	mockAddrCodec := internal.NewMockAddressCodecHex(t)

	solanaChain := cciptypes.ChainSelector(sel.SOLANA_DEVNET.Selector)
	tokenAddress := "0x1234567890abcdef1234567890abcdef12345678"

	// Mock the address codec to handle the conversion
	mockAddrCodec.EXPECT().AddressStringToBytes(tokenAddress, solanaChain).Return([]byte(tokenAddress), nil)
	mockAddrCodec.EXPECT().AddressBytesToString(mock.Anything, solanaChain).Return(tokenAddress, nil)

	// Create mock chain accessor
	mockAccessor := mockChainAccessor.NewMockChainAccessor(t)
	mockAccessor.EXPECT().Sync(ctx, consts.ContractNameUSDCTokenPool, mock.Anything).Return(nil)

	chainAccessors := map[cciptypes.ChainSelector]cciptypes.ChainAccessor{
		solanaChain: mockAccessor,
	}

	tokensConfig := map[cciptypes.ChainSelector]pluginconfig.USDCCCTPTokenConfig{
		solanaChain: {
			SourcePoolAddress:            tokenAddress,
			SourceMessageTransmitterAddr: tokenAddress,
		},
	}

	// Set looppCCIPProviderSupported to true for Solana
	looppCCIPProviderSupported := map[string]bool{
		sel.FamilySolana: true,
	}

	emptyContractReaders := make(map[cciptypes.ChainSelector]contractreader.Extended)

	r, err := NewUSDCMessageReader(
		ctx,
		logger.Test(t),
		tokensConfig,
		looppCCIPProviderSupported,
		chainAccessors,
		emptyContractReaders,
		mockAddrCodec,
	)
	require.NoError(t, err)
	require.NotNil(t, r)

	compositeReader, ok := r.(compositeUSDCMessageReader)
	require.True(t, ok)

	solanaReader, exists := compositeReader.readers[solanaChain]
	require.True(t, exists)
	require.NotNil(t, solanaReader)

	// Verify it created a reader of type solanaUSDCMessageReaderAccessor
	_, ok = solanaReader.(solanaUSDCMessageReaderAccessor)
	require.True(t, ok)
}
